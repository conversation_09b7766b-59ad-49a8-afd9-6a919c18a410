<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Improved User Cards Test</title>
    <link rel="stylesheet" href="css/admin/admin-dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: rgb(11, 8, 16);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .test-title {
            color: #1a7de8;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(26, 125, 232, 0.5);
        }
        .test-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.2rem;
        }
        .comparison-section {
            margin-bottom: 50px;
        }
        .section-title {
            color: #fff;
            font-size: 1.8rem;
            margin-bottom: 20px;
            text-align: center;
        }
        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 24px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">Enhanced User Cards</h1>
            <p class="test-subtitle">Improved design with unified filter container and better user experience</p>
        </div>

        <div class="comparison-section">
            <h2 class="section-title">Sample User Cards</h2>
            <div class="users-grid">
                
                <!-- Pending User Card -->
                <div class="user-card">
                    <div class="user-selection" onclick="event.stopPropagation()">
                        <input type="checkbox" class="user-checkbox" data-user-id="1">
                    </div>



                    <div class="user-profile-section">
                        <div class="user-avatar">
                            <img src="images/profile-icon.png" alt="john_doe">
                            <div class="user-level-badge">3</div>
                        </div>
                        <div class="user-basic-info">
                            <h3 class="user-name">john_doe</h3>
                            <p class="user-email"><EMAIL></p>
                            <div class="user-meta">
                                <span class="join-date">
                                    <i class="fas fa-calendar-alt"></i>
                                    Joined Dec 15, 2024
                                </span>
                                <span class="full-name">
                                    <i class="fas fa-user"></i>
                                    John Doe
                                </span>
                                <!-- Status badges positioned below join date -->
                                <div class="user-status-info">
                                    <div class="status-badge pending-badge">
                                        <i class="fas fa-clock"></i>
                                        <span>Pending</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="user-progress-stats">
                        <div class="progress-item">
                            <div class="progress-icon achievements-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">5/50</div>
                                <div class="progress-label">Achievements</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 10%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="progress-item">
                            <div class="progress-icon levels-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">2/6</div>
                                <div class="progress-label">Levels Completed</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 33%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="progress-item experience-item">
                            <div class="progress-icon experience-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">250/400 EXP</div>
                                <div class="progress-label">Experience Level 3</div>
                                <div class="progress-bar experience-bar">
                                    <div class="progress-fill experience-fill" style="width: 62%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="user-quick-actions">
                        <button class="quick-action-btn view-btn">
                            <i class="fas fa-eye"></i>
                            <span>View</span>
                        </button>
                        <button class="quick-action-btn approve-btn">
                            <i class="fas fa-check"></i>
                            <span>Approve</span>
                        </button>
                        <button class="quick-action-btn reject-btn">
                            <i class="fas fa-times"></i>
                            <span>Reject</span>
                        </button>
                    </div>
                </div>

                <!-- Approved User Card -->
                <div class="user-card">
                    <div class="user-selection" onclick="event.stopPropagation()">
                        <input type="checkbox" class="user-checkbox" data-user-id="2">
                    </div>



                    <div class="user-profile-section">
                        <div class="user-avatar">
                            <img src="images/profile-icon.png" alt="jane_smith">
                            <div class="user-level-badge">8</div>
                        </div>
                        <div class="user-basic-info">
                            <h3 class="user-name">jane_smith</h3>
                            <p class="user-email"><EMAIL></p>
                            <div class="user-meta">
                                <span class="join-date">
                                    <i class="fas fa-calendar-alt"></i>
                                    Joined Nov 28, 2024
                                </span>
                                <span class="full-name">
                                    <i class="fas fa-user"></i>
                                    Jane Smith
                                </span>
                                <!-- Status badges positioned below join date -->
                                <div class="user-status-info">
                                    <div class="status-badge approved-badge">
                                        <i class="fas fa-check-circle"></i>
                                        <span>Approved</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="user-progress-stats">
                        <div class="progress-item">
                            <div class="progress-icon achievements-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">28/50</div>
                                <div class="progress-label">Achievements</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 56%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="progress-item">
                            <div class="progress-icon levels-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">6/6</div>
                                <div class="progress-label">Levels Completed</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 100%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="progress-item experience-item">
                            <div class="progress-icon experience-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">1250/1500 EXP</div>
                                <div class="progress-label">Experience Level 8</div>
                                <div class="progress-bar experience-bar">
                                    <div class="progress-fill experience-fill" style="width: 83%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="user-quick-actions">
                        <button class="quick-action-btn view-btn">
                            <i class="fas fa-eye"></i>
                            <span>View</span>
                        </button>
                        <button class="quick-action-btn edit-btn">
                            <i class="fas fa-edit"></i>
                            <span>Edit</span>
                        </button>
                        <button class="quick-action-btn deactivate-btn">
                            <i class="fas fa-ban"></i>
                            <span>Deactivate</span>
                        </button>
                    </div>
                </div>

                <!-- Rejected User Card -->
                <div class="user-card">
                    <div class="user-selection" onclick="event.stopPropagation()">
                        <input type="checkbox" class="user-checkbox" data-user-id="3">
                    </div>



                    <div class="user-profile-section">
                        <div class="user-avatar">
                            <img src="images/profile-icon.png" alt="rejected_user">
                            <div class="user-level-badge">1</div>
                        </div>
                        <div class="user-basic-info">
                            <h3 class="user-name">rejected_user</h3>
                            <p class="user-email"><EMAIL></p>
                            <div class="user-meta">
                                <span class="join-date">
                                    <i class="fas fa-calendar-alt"></i>
                                    Joined Dec 20, 2024
                                </span>
                                <!-- Status badges positioned below join date -->
                                <div class="user-status-info">
                                    <div class="status-badge rejected-badge">
                                        <i class="fas fa-times-circle"></i>
                                        <span>Rejected</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="user-progress-stats">
                        <div class="progress-item">
                            <div class="progress-icon achievements-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">0/50</div>
                                <div class="progress-label">Achievements</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="progress-item">
                            <div class="progress-icon levels-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">0/6</div>
                                <div class="progress-label">Levels Completed</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="progress-item experience-item">
                            <div class="progress-icon experience-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">0/100 EXP</div>
                                <div class="progress-label">Experience Level 1</div>
                                <div class="progress-bar experience-bar">
                                    <div class="progress-fill experience-fill" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="user-quick-actions">
                        <button class="quick-action-btn view-btn">
                            <i class="fas fa-eye"></i>
                            <span>View</span>
                        </button>
                        <button class="quick-action-btn delete-btn">
                            <i class="fas fa-trash"></i>
                            <span>Delete</span>
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script>
        // Add some interactivity for testing
        document.querySelectorAll('.quick-action-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const action = this.querySelector('span').textContent;
                const userCard = this.closest('.user-card');
                const userName = userCard.querySelector('.user-name').textContent;
                
                // Simple feedback
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                    alert(`${action} action clicked for user: ${userName}`);
                }, 150);
            });
        });

        // Add checkbox functionality
        document.querySelectorAll('.user-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const userCard = this.closest('.user-card');
                if (this.checked) {
                    userCard.classList.add('selected');
                } else {
                    userCard.classList.remove('selected');
                }
            });
        });
    </script>
</body>
</html>
