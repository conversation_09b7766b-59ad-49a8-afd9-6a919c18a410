<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Content Management</title>
    <link rel="stylesheet" href="../../css/admin/admin-dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-shield-alt"></i> Admin Panel</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="user-management.html">
                        <i class="fas fa-users"></i> Users Management
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="#">
                        <i class="fas fa-gamepad"></i> Game Content
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Game Content Management Section -->
            <section id="content-section" class="content-section active">
                <div class="section-header">
                    <h1>Game Content Management</h1>
                    <div class="search-bar">
                        <input type="text" id="levelSearch" placeholder="Search by level number..." onkeyup="searchLevels()">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
                <div class="content-controls">
                    <div class="content-info">
                        <span class="info-text">Manage game levels and their questions</span>
                    </div>
                    <div class="level-controls">
                        <button class="btn-primary" onclick="showAddLevelModal()">
                            <i class="fas fa-plus"></i> Add Level
                        </button>
                        <button class="btn-warning" onclick="showRearrangeLevelsModal()">
                            <i class="fas fa-sort"></i> Rearrange Levels
                        </button>
                        <button class="btn-secondary" id="viewAllQuestions" onclick="showAllQuestionsModal()">
                            <i class="fas fa-list"></i> View All Questions
                        </button>
                    </div>
                </div>
                <!-- Game Levels List -->
                <div class="levels-container" id="levelsContainer">
                    <!-- Levels will be populated by JavaScript -->
                </div>
            </section>
        </main>
    </div>

    <!-- Add Level Modal -->
    <div id="addLevelModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Add New Level</h2>
                <span class="close" onclick="closeModal('addLevelModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addLevelForm">
                    <div class="form-group">
                        <label for="levelNumber">Level Number:</label>
                        <input type="number" id="levelNumber" min="1" required readonly>
                        <small class="form-help">Next available level number</small>
                    </div>
                    <div class="form-group">
                        <label for="gameCategory">Game Category:</label>
                        <select id="gameCategory" required>
                            <option value="">Select a game type...</option>
                            <option value="hangman">Hangman (Robot Rescue)</option>
                            <option value="matching">Matching Game</option>
                            <option value="millionaire">Millionaire Quiz</option>
                            <option value="robot-battle">Robot Battle</option>
                        </select>
                        <small class="form-help">Choose which game template to copy</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal('addLevelModal')">Cancel</button>
                <button type="button" class="btn-primary" onclick="addLevel()">Create Level</button>
            </div>
        </div>
    </div>

    <!-- Delete Level Confirmation Modal -->
    <div id="deleteLevelModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Delete Level Confirmation</h2>
                <span class="close" onclick="closeModal('deleteLevelModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="warning-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Are you sure you want to delete Level <span id="deleteLevelNumber"></span>?</h3>
                </div>
                <div class="delete-consequences">
                    <p><strong>This action will permanently:</strong></p>
                    <ul>
                        <li><i class="fas fa-file"></i> Delete the level file (game<span id="deleteFileName"></span>.html)</li>
                        <li><i class="fas fa-question-circle"></i> Delete all <span id="deleteQuestionCount">0</span> questions for this level</li>
                        <li><i class="fas fa-user"></i> Delete all user progress for this level</li>
                    </ul>
                    <p class="warning-text"><strong>This action cannot be undone!</strong></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal('deleteLevelModal')">Cancel</button>
                <button type="button" class="btn-danger" onclick="confirmDeleteLevel()" id="confirmDeleteBtn">
                    <i class="fas fa-trash"></i> Delete Level
                </button>
            </div>
        </div>
    </div>

    <!-- Rearrange Levels Modal -->
    <div id="rearrangeLevelsModal" class="modal">
        <div class="modal-content rearrange-modal">
            <div class="modal-header">
                <h2>Rearrange Levels</h2>
                <span class="close" onclick="closeModal('rearrangeLevelsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="rearrange-instructions">
                    <p><i class="fas fa-info-circle"></i> Use the controls below to easily rearrange your game levels.</p>
                </div>

                <!-- Quick Swap Section -->
                <div class="quick-swap-section">
                    <h3>Quick Swap</h3>
                    <div class="swap-controls">
                        <div class="swap-input-group">
                            <label>Swap Level:</label>
                            <select id="swapLevel1">
                                <option value="">Select level...</option>
                            </select>
                        </div>
                        <div class="swap-arrow">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="swap-input-group">
                            <label>With Level:</label>
                            <select id="swapLevel2">
                                <option value="">Select level...</option>
                            </select>
                        </div>
                        <button class="btn-primary" onclick="performQuickSwap()">
                            <i class="fas fa-exchange-alt"></i> Swap
                        </button>
                    </div>
                </div>

                <!-- Move Questions Between Levels Section -->
                <div class="move-questions-section">
                    <h3>Move Questions Between Levels</h3>
                    <div class="move-questions-controls">
                        <div class="question-source-group">
                            <label for="sourceQuestionLevel">From Level:</label>
                            <select id="sourceQuestionLevel">
                                <option value="">Select source level...</option>
                            </select>
                        </div>
                        <div class="question-target-group">
                            <label for="targetQuestionLevel">To Level:</label>
                            <select id="targetQuestionLevel">
                                <option value="">Select target level...</option>
                            </select>
                        </div>
                        <button type="button" class="btn-primary" onclick="showMoveQuestionsModal()">
                            <i class="fas fa-exchange-alt"></i> Select Questions
                        </button>
                    </div>
                    <div class="move-questions-info">
                        <p><i class="fas fa-info-circle"></i> Select source and target levels, then choose which questions to move.</p>
                    </div>
                </div>

                <!-- Bulk Operations Section -->
                <div class="bulk-operations-section">
                    <h3>Bulk Operations</h3>
                    <div class="bulk-controls">
                        <button type="button" class="btn-warning" onclick="reverseAllLevels()">
                            <i class="fas fa-sort-amount-down-alt"></i> Reverse Order
                        </button>
                        <button type="button" class="btn-info" onclick="sortLevelsByQuestions()">
                            <i class="fas fa-sort-numeric-down"></i> Sort by Question Count
                        </button>
                        <button type="button" class="btn-success" onclick="compactLevels()">
                            <i class="fas fa-compress-alt"></i> Compact Levels (Remove Gaps)
                        </button>
                    </div>
                </div>

                <!-- Level Management Section -->
                <div class="level-management-section">
                    <h3>Level Management</h3>
                    <div class="level-management-info">
                        <p><i class="fas fa-info-circle"></i> Use the buttons below to quickly move levels up, down, to top, or to bottom.</p>
                    </div>
                    <div class="insert-levels" id="insertLevels">
                        <!-- Levels will be populated here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal('rearrangeLevelsModal')">Close</button>
            </div>
        </div>
    </div>

    <!-- Level Questions Modal -->
    <div id="levelQuestionsModal" class="modal">
        <div class="modal-content level-questions-modal">
            <div class="modal-header">
                <span class="close-top-right" onclick="closeModal('levelQuestionsModal')" title="Close Modal">&times;</span>
                <div class="modal-title-section">
                    <h2 id="levelModalTitle">Level Questions</h2>
                    <p id="levelModalSubtitle" class="modal-subtitle">Manage questions for this level</p>
                </div>
            </div>
            <div class="modal-body">
                <div class="level-info-summary">
                    <div class="total-questions-display">
                        <span class="questions-count-text">Total Questions: </span>
                        <span class="questions-count-number" id="levelQuestionCount">0</span>
                    </div>
                </div>

                <div class="level-questions-container" id="levelQuestionsContainer">
                    <!-- Level questions will be populated here -->
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-left">
                    <span class="footer-info" id="questionsFooterInfo">No questions added yet</span>
                </div>
                <div class="modal-footer-right">
                    <button type="button" class="btn-primary" onclick="showAddQuestionModalFromLevel()" id="addQuestionFromLevelBtn2">
                        <i class="fas fa-plus"></i> Add Question
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- View All Questions Modal -->
    <div id="allQuestionsModal" class="modal">
        <div class="modal-content all-questions-modal">
            <div class="modal-header">
                <h2>All Questions Overview</h2>
                <span class="close" onclick="closeModal('allQuestionsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="questions-summary">
                    <div class="summary-stats">
                        <div class="stat-item">
                            <span class="stat-number" id="totalLevels">0</span>
                            <span class="stat-label">Levels</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="totalQuestions">0</span>
                            <span class="stat-label">Questions</span>
                        </div>
                    </div>
                </div>

                <div class="questions-filter">
                    <input type="text" id="questionsSearch" placeholder="Search questions..." onkeyup="filterAllQuestions()">
                    <select id="levelFilter" onchange="filterAllQuestions()">
                        <option value="">All Levels</option>
                    </select>
                    <select id="typeFilter" onchange="filterAllQuestions()">
                        <option value="">All Types</option>
                        <option value="Multiple Choice">Multiple Choice</option>
                        <option value="Matching Type">Matching Type</option>
                    </select>
                </div>

                <div class="all-questions-container" id="allQuestionsContainer">
                    <!-- Questions will be populated here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal('allQuestionsModal')">Close</button>
            </div>
        </div>
    </div>

    <!-- Question Modal -->
    <div id="questionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="questionModalTitle">Add Question</h2>
                <span class="close" onclick="closeModal('questionModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="questionForm">
                    <div class="form-group">
                        <label for="quizType">Quiz Type:</label>
                        <select id="quizType" required>
                            <option value="Multiple Choice">Multiple Choice</option>
                            <option value="Matching Type">Matching Type</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="questionText">Question:</label>
                        <textarea id="questionText" required></textarea>
                    </div>
                    <div class="form-group option-field">
                        <label for="option1">Option A:</label>
                        <input type="text" id="option1">
                    </div>
                    <div class="form-group option-field">
                        <label for="option2">Option B:</label>
                        <input type="text" id="option2">
                    </div>
                    <div class="form-group option-field">
                        <label for="option3">Option C:</label>
                        <input type="text" id="option3">
                    </div>
                    <div class="form-group option-field">
                        <label for="option4">Option D:</label>
                        <input type="text" id="option4">
                    </div>
                    <div class="form-group">
                        <label for="correctAnswer">Correct Answer:</label>
                        <input type="text" id="correctAnswer" required placeholder="Type the exact answer here">
                        <small class="info-text" style="color:#888;display:block;margin-top:4px;">The correct answer must exactly match one of the options above (case-sensitive).</small>
                    </div>
                    <div class="form-group">
                        <label for="levelNumber">Level Number:</label>
                        <input type="number" id="levelNumber" min="1" required>
                    </div>
                    <input type="hidden" id="contentId" value="">
                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="closeModal('questionModal')">Cancel</button>
                        <button type="submit" class="btn-primary">OK</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Move Questions Modal -->
    <div id="moveQuestionsModal" class="modal">
        <div class="modal-content move-questions-modal">
            <div class="modal-header">
                <h2>Move Questions Between Levels</h2>
                <span class="close" onclick="closeModal('moveQuestionsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="move-questions-summary">
                    <div class="source-level-info">
                        <h4>From: <span id="sourceLevelName">Level X</span></h4>
                        <p><span id="sourceLevelQuestionCount">0</span> questions available</p>
                    </div>
                    <div class="move-arrow">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                    <div class="target-level-info">
                        <h4>To: <span id="targetLevelName">Level Y</span></h4>
                        <p><span id="targetLevelQuestionCount">0</span> questions currently</p>
                    </div>
                </div>

                <div class="questions-selection">
                    <div class="selection-controls">
                        <button type="button" class="btn-small btn-info" onclick="selectAllQuestions()">
                            <i class="fas fa-check-double"></i> Select All
                        </button>
                        <button type="button" class="btn-small btn-secondary" onclick="deselectAllQuestions()">
                            <i class="fas fa-times"></i> Deselect All
                        </button>
                        <span class="selected-count">0 questions selected</span>
                    </div>

                    <div class="questions-list" id="moveQuestionsContainer">
                        <!-- Questions will be populated here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal('moveQuestionsModal')">Cancel</button>
                <button type="button" class="btn-primary" onclick="executeQuestionMove()" id="moveQuestionsBtn" disabled>
                    <i class="fas fa-exchange-alt"></i> Move Selected Questions
                </button>
            </div>
        </div>
    </div>

    <script src="../../js/admin/game-management.js"></script>
</body>
</html>
