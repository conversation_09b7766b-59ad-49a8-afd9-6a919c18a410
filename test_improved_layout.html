<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Improved Layout Test</title>
    <link rel="stylesheet" href="css/admin/admin-dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: rgb(11, 8, 16);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .test-title {
            color: #1a7de8;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(26, 125, 232, 0.5);
        }
        .test-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.2rem;
        }
        .feature-highlight {
            background: rgba(26, 125, 232, 0.1);
            border: 1px solid rgba(26, 125, 232, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
        }
        .feature-list li i {
            color: #27ae60;
            width: 20px;
        }
        .demo-section {
            margin-bottom: 40px;
        }
        .section-title {
            color: #fff;
            font-size: 1.8rem;
            margin-bottom: 20px;
            text-align: center;
        }
        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 24px;
            margin-bottom: 30px;
        }
        .demo-controls {
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-btn {
            background: linear-gradient(135deg, #1a7de8, #1565C0);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(26, 125, 232, 0.4);
        }
        .demo-btn.active {
            background: linear-gradient(135deg, #27ae60, #229954);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">Improved User Management Layout</h1>
            <p class="test-subtitle">Status badges repositioned & context-aware bulk operations</p>
        </div>

        <div class="feature-highlight">
            <h3 style="color: #1a7de8; margin-bottom: 15px;">Key Improvements</h3>
            <ul class="feature-list">
                <li><i class="fas fa-check"></i> Status badges moved below join date for better information flow</li>
                <li><i class="fas fa-check"></i> Checkboxes hidden in "All Users" view (bulk operations not applicable)</li>
                <li><i class="fas fa-check"></i> Context-aware bulk operations based on current filter</li>
                <li><i class="fas fa-check"></i> Pending/Rejected users show "Bulk Approve" option</li>
                <li><i class="fas fa-check"></i> Approved users show "Bulk Disapprove" option</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2 class="section-title">Filter Simulation</h2>
            <div class="demo-controls">
                <button class="demo-btn active" onclick="simulateFilter('all')">All Users</button>
                <button class="demo-btn" onclick="simulateFilter('pending')">Pending</button>
                <button class="demo-btn" onclick="simulateFilter('approved')">Approved</button>
                <button class="demo-btn" onclick="simulateFilter('rejected')">Rejected</button>
            </div>

            <!-- Bulk Operations Demo -->
            <div class="bulk-operations" id="demoBulkOperations" style="display: none;">
                <div class="bulk-controls">
                    <span id="demoSelectedCount">2 users selected</span>
                    <button id="demoBulkApproveBtn" class="bulk-btn approve-btn" style="display: none;">
                        <i class="fas fa-check"></i> Bulk Approve
                    </button>
                    <button id="demoBulkDisapproveBtn" class="bulk-btn reject-btn" style="display: none;">
                        <i class="fas fa-times"></i> Bulk Disapprove
                    </button>
                    <button class="bulk-btn clear-btn">
                        <i class="fas fa-times-circle"></i> Clear Selection
                    </button>
                </div>
            </div>

            <div class="users-grid" id="demoUsersGrid">
                
                <!-- All Users View - No Checkboxes -->
                <div class="user-card demo-card" data-filter="all">
                    <!-- No checkbox for all users view -->
                    <div class="user-profile-section">
                        <div class="user-avatar">
                            <img src="images/profile-icon.png" alt="john_doe">
                            <div class="user-level-badge">3</div>
                        </div>
                        <div class="user-basic-info">
                            <h3 class="user-name">john_doe</h3>
                            <p class="user-email"><EMAIL></p>
                            <div class="user-meta">
                                <span class="join-date">
                                    <i class="fas fa-calendar-alt"></i>
                                    Joined Dec 15, 2024
                                </span>
                                <span class="full-name">
                                    <i class="fas fa-user"></i>
                                    John Doe
                                </span>
                                <!-- Status badges positioned below join date -->
                                <div class="user-status-info">
                                    <div class="status-badge pending-badge">
                                        <i class="fas fa-clock"></i>
                                        <span>Pending</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="user-progress-stats">
                        <div class="progress-item">
                            <div class="progress-icon achievements-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">5/50</div>
                                <div class="progress-label">Achievements</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 10%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="progress-item">
                            <div class="progress-icon levels-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">2/6</div>
                                <div class="progress-label">Levels Completed</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 33%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="progress-item experience-item">
                            <div class="progress-icon experience-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">250/400 EXP</div>
                                <div class="progress-label">Experience Level 3</div>
                                <div class="progress-bar experience-bar">
                                    <div class="progress-fill experience-fill" style="width: 62%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="user-quick-actions">
                        <button class="quick-action-btn view-btn">
                            <i class="fas fa-eye"></i>
                            <span>View</span>
                        </button>
                        <button class="quick-action-btn approve-btn">
                            <i class="fas fa-check"></i>
                            <span>Approve</span>
                        </button>
                        <button class="quick-action-btn reject-btn">
                            <i class="fas fa-times"></i>
                            <span>Reject</span>
                        </button>
                    </div>
                </div>

                <!-- Pending Users View - With Checkboxes -->
                <div class="user-card demo-card" data-filter="pending" style="display: none;">
                    <div class="user-selection">
                        <input type="checkbox" class="user-checkbox demo-checkbox" checked>
                    </div>
                    <div class="user-profile-section">
                        <div class="user-avatar">
                            <img src="images/profile-icon.png" alt="pending_user">
                            <div class="user-level-badge">1</div>
                        </div>
                        <div class="user-basic-info">
                            <h3 class="user-name">pending_user</h3>
                            <p class="user-email"><EMAIL></p>
                            <div class="user-meta">
                                <span class="join-date">
                                    <i class="fas fa-calendar-alt"></i>
                                    Joined Dec 20, 2024
                                </span>
                                <div class="user-status-info">
                                    <div class="status-badge pending-badge">
                                        <i class="fas fa-clock"></i>
                                        <span>Pending</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="user-progress-stats">
                        <div class="progress-item">
                            <div class="progress-icon achievements-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">0/50</div>
                                <div class="progress-label">Achievements</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="progress-item">
                            <div class="progress-icon levels-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">0/6</div>
                                <div class="progress-label">Levels Completed</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="progress-item experience-item">
                            <div class="progress-icon experience-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">0/100 EXP</div>
                                <div class="progress-label">Experience Level 1</div>
                                <div class="progress-bar experience-bar">
                                    <div class="progress-fill experience-fill" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="user-quick-actions">
                        <button class="quick-action-btn view-btn">
                            <i class="fas fa-eye"></i>
                            <span>View</span>
                        </button>
                        <button class="quick-action-btn approve-btn">
                            <i class="fas fa-check"></i>
                            <span>Approve</span>
                        </button>
                        <button class="quick-action-btn reject-btn">
                            <i class="fas fa-times"></i>
                            <span>Reject</span>
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script>
        let currentFilter = 'all';

        function simulateFilter(filter) {
            currentFilter = filter;
            
            // Update active button
            document.querySelectorAll('.demo-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Show/hide cards based on filter
            document.querySelectorAll('.demo-card').forEach(card => {
                if (card.dataset.filter === filter || filter === 'all') {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
            
            // Update bulk operations
            updateDemoBulkOperations();
        }

        function updateDemoBulkOperations() {
            const bulkOps = document.getElementById('demoBulkOperations');
            const approveBtn = document.getElementById('demoBulkApproveBtn');
            const disapproveBtn = document.getElementById('demoBulkDisapproveBtn');
            
            if (currentFilter === 'all') {
                bulkOps.style.display = 'none';
            } else {
                const hasChecked = document.querySelector('.demo-checkbox:checked');
                if (hasChecked) {
                    bulkOps.style.display = 'block';
                    
                    if (currentFilter === 'pending' || currentFilter === 'rejected') {
                        approveBtn.style.display = 'inline-flex';
                        disapproveBtn.style.display = 'none';
                    } else if (currentFilter === 'approved') {
                        approveBtn.style.display = 'none';
                        disapproveBtn.style.display = 'inline-flex';
                    }
                } else {
                    bulkOps.style.display = 'none';
                }
            }
        }

        // Initialize
        simulateFilter('all');
        
        // Add checkbox listeners
        document.querySelectorAll('.demo-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateDemoBulkOperations);
        });
    </script>
</body>
</html>
