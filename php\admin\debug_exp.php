<?php
header('Content-Type: text/plain');

require_once '../dbconnection.php';

$database = new Database();
$conn = $database->getConnection();

// Test the experience level calculation
$userExp = 102;

echo "Testing userExp = $userExp\n\n";

// Show all exp levels
$query = "SELECT expID, expName, expNeeded FROM exp ORDER BY expNeeded ASC";
$stmt = $conn->prepare($query);
$stmt->execute();
$allLevels = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "All experience levels:\n";
foreach ($allLevels as $level) {
    echo "expID: {$level['expID']}, expName: {$level['expName']}, expNeeded: {$level['expNeeded']}\n";
}

echo "\n";

// Test the current query
$query = "SELECT expID, expName, expNeeded FROM exp WHERE expNeeded <= ? ORDER BY expNeeded DESC LIMIT 1";
$stmt = $conn->prepare($query);
$stmt->execute([$userExp]);
$result = $stmt->fetch(PDO::FETCH_ASSOC);

echo "Query: SELECT expID, expName, expNeeded FROM exp WHERE expNeeded <= $userExp ORDER BY expNeeded DESC LIMIT 1\n";
echo "Result: ";
if ($result) {
    echo "expID: {$result['expID']}, expName: {$result['expName']}, expNeeded: {$result['expNeeded']}\n";
} else {
    echo "No result found\n";
}

echo "\n";

// Show which levels qualify
$query = "SELECT expID, expName, expNeeded FROM exp WHERE expNeeded <= ? ORDER BY expNeeded DESC";
$stmt = $conn->prepare($query);
$stmt->execute([$userExp]);
$qualifyingLevels = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "All qualifying levels (expNeeded <= $userExp):\n";
foreach ($qualifyingLevels as $level) {
    echo "expID: {$level['expID']}, expName: {$level['expName']}, expNeeded: {$level['expNeeded']}\n";
}
?>
