# User Approval Menu Guide

## Overview
The User Management system now includes a new approval status menu that allows administrators to easily separate and manage users based on their approval status. This enhancement improves the workflow for handling user account approvals and rejections.

## Features

### Approval Status Tabs
The system now includes four main tabs to categorize users:

1. **All Users** - Shows all users regardless of approval status
2. **Pending Approval** - Shows users waiting for approval (is_approved = 0)
3. **Approved** - Shows users that have been approved (is_approved = 1)
4. **Rejected** - Shows users that have been rejected (is_approved = -1)

### Real-time Counts
Each tab displays the current count of users in that category, updating automatically when:
- Users are approved or rejected
- New users register
- Users are deleted
- Bulk operations are performed

### Enhanced Filtering
- The search functionality now works within the selected approval status filter
- Users can search for specific users within each approval category
- Filters persist during search operations

## Technical Implementation

### Frontend Changes

#### HTML Structure
- Added approval menu with tab buttons in `html/admin/user-management.html`
- Each tab includes an icon, label, and count display
- Responsive design for mobile devices

#### CSS Styling
- New styles in `css/admin/admin-dashboard.css` for the approval menu
- Consistent design with existing admin interface
- Hover effects and active state styling
- Mobile-responsive tab layout

#### JavaScript Functionality
- New filtering functions in `js/admin/user-management.js`
- Global state management for current approval filter
- Automatic count updates after user operations
- Enhanced search functionality with approval status filtering

### Backend Changes

#### API Enhancement
- Added new endpoint `get_users_by_approval_status` in `php/admin/users_api.php`
- Supports filtering by 'pending', 'approved', or 'rejected' status
- Maintains existing functionality while adding new capabilities

## Usage Instructions

### For Administrators

1. **Navigate to User Management**
   - Access the admin panel
   - Click on "Users Management" in the sidebar

2. **Use Approval Status Tabs**
   - Click on any tab to filter users by approval status
   - The active tab is highlighted in blue
   - User counts are displayed on each tab

3. **Search Within Categories**
   - Select an approval status tab first
   - Use the search bar to find specific users within that category
   - Search works across username, email, and name fields

4. **Manage Users**
   - All existing user management functions work within filtered views
   - Bulk operations (approve/disapprove) work with filtered selections
   - Individual user actions (approve, reject, edit, delete) update counts automatically

### Workflow Examples

#### Handling New Registrations
1. Click "Pending Approval" tab to see all users awaiting approval
2. Review user details by clicking on user cards
3. Use bulk approval for multiple users or individual approval for single users
4. Counts automatically update after approval actions

#### Managing Rejected Users
1. Click "Rejected" tab to see all rejected users
2. Review rejected users if needed
3. Delete users that are no longer needed
4. Re-approve users if rejection was made in error

## Benefits

### Improved Workflow
- Clear separation of users by approval status
- Faster identification of users requiring attention
- Streamlined bulk operations for pending users

### Better Organization
- Visual indicators for each approval category
- Real-time counts for better overview
- Persistent filtering during search operations

### Enhanced User Experience
- Intuitive tab-based navigation
- Consistent design with existing interface
- Mobile-friendly responsive design

## Compatibility

### Browser Support
- Modern browsers with CSS Grid and Flexbox support
- Mobile browsers with touch interaction
- Responsive design for various screen sizes

### Database Compatibility
- Works with existing user_account table structure
- Uses existing is_approved field values (0, 1, -1)
- No database schema changes required

## Future Enhancements

### Potential Improvements
- Add date-based filtering within approval categories
- Export functionality for each approval status
- Email notifications for pending approvals
- Advanced sorting options within each category

### Performance Considerations
- Current implementation uses client-side filtering for better responsiveness
- For large user bases, consider server-side filtering using the new API endpoint
- Pagination works within filtered views

## Troubleshooting

### Common Issues
1. **Counts not updating**: Refresh the page or check browser console for JavaScript errors
2. **Tabs not switching**: Ensure JavaScript is enabled and check for console errors
3. **Search not working within filters**: Clear search and try again, or refresh the page

### Support
For technical issues or questions about the approval menu functionality, refer to the main user management documentation or contact the development team.
