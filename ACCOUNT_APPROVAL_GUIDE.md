# Account Approval System - User Guide

## Overview
The system now includes an account approval feature that requires admin approval for new user accounts before they can access the platform.

## Account Status Types

### 1. **Pending Approval** (is_approved = 0)
- **Default Status**: All new accounts start with this status
- **User Experience**: Cannot login, receives "pending approval" message
- **Visual Indicator**: Yellow "Pending" badge in admin interface
- **Admin Actions**: Can approve or reject the account

### 2. **Approved** (is_approved = 1)
- **Status**: Account has been approved by an administrator
- **User Experience**: Can login and access all features normally
- **Visual Indicator**: Green "Approved" badge in admin interface
- **Admin Actions**: Can ban or delete if needed

### 3. **Rejected** (is_approved = -1)
- **Status**: Account has been rejected by an administrator
- **User Experience**: Cannot login, receives "account rejected" message
- **Visual Indicator**: Red "Rejected" badge in admin interface
- **Admin Actions**: Account is effectively disabled

## User Registration Flow

### New User Signup Process
1. User fills out signup form with username and password
2. System creates account with `is_approved = 0` (pending)
3. User receives success message: "Account created successfully! Your account is pending admin approval."
4. User is redirected to login page with approval pending notice

### Login Attempts
- **Pending Account**: "Your account is pending admin approval. Please wait for approval before logging in."
- **Rejected Account**: "Your account has been rejected by an administrator. Please contact support."
- **Approved Account**: Normal login process continues

## Admin Management Interface

### User Status Display
- **User Cards**: Show approval status badges (Pending/Approved/Rejected)
- **User Details Modal**: Displays "Account Status" field with current approval state
- **Color Coding**:
  - 🟡 Yellow: Pending approval
  - 🟢 Green: Approved
  - 🔴 Red: Rejected

### Admin Actions

#### Approving Accounts
1. Navigate to User Management in admin panel
2. Click on a user with "Pending" status
3. Click "Approve Account" button in the user details modal
4. Confirm the approval action
5. User can now login normally

#### Rejecting Accounts
1. Navigate to User Management in admin panel
2. Click on a user with "Pending" status
3. Click "Reject Account" button in the user details modal
4. Confirm the rejection action
5. User will be unable to login and receive rejection message

## Database Schema

### user_account Table
```sql
is_approved INT(11) NULL
```

**Values:**
- `0`: Pending approval (default for new accounts)
- `1`: Approved (can login)
- `-1`: Rejected (cannot login)

## API Endpoints

### Approval Operations
- **Approve User**: `PUT /php/admin/users_api.php?action=approve_user`
- **Reject User**: `PUT /php/admin/users_api.php?action=reject_user`

### Request Format
```json
{
    "user_id": 123
}
```

### Response Format
```json
{
    "success": true,
    "message": "User approved successfully"
}
```

## Security Features

### Login Protection
- Checks approval status before allowing login
- Prevents access for pending/rejected accounts
- Maintains existing ban system alongside approval system

### Admin Validation
- Confirmation dialogs for approval/rejection actions
- Real-time UI updates after status changes
- Proper error handling and user feedback

## User Experience Improvements

### Clear Communication
- Informative messages at each step of the process
- Visual status indicators in admin interface
- Helpful error messages during login attempts

### Seamless Integration
- Works alongside existing ban system
- Maintains all existing user management features
- No disruption to approved user experience

## Implementation Details

### Files Modified
- `php/signup.php` - Sets default is_approved=0
- `php/login.php` - Checks approval status
- `php/admin/users_api.php` - Added approval endpoints
- `js/login.js` - Added approval error messages
- `js/admin/user-management.js` - Added approval UI
- `css/admin/admin-dashboard.css` - Added approval styling

### Database Changes
- Uses existing `is_approved` column in `user_account` table
- No schema changes required if column exists
- Default value set to 0 for new accounts

## Best Practices

### For Administrators
1. **Regular Review**: Check pending accounts regularly
2. **Clear Criteria**: Establish clear approval criteria
3. **Timely Response**: Process approvals promptly to avoid user frustration
4. **Documentation**: Keep records of approval/rejection reasons

### For Users
1. **Patience**: Allow time for admin review after signup
2. **Contact Info**: Provide way for users to contact support if needed
3. **Clear Instructions**: Inform users about the approval process

## Troubleshooting

### Common Issues
- **User can't login**: Check if account is approved
- **Approval buttons not showing**: Ensure user status is pending (0)
- **Database errors**: Verify is_approved column exists and has correct permissions

### Error Messages
- Login errors are displayed via modal dialogs
- Admin actions show success/error notifications
- All operations include proper error handling

This approval system provides administrators with complete control over user access while maintaining a smooth user experience for approved accounts.
