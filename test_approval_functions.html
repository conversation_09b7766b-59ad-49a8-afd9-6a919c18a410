<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Approval Functions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>User Management Approval Functions Test</h1>
    
    <div class="test-section">
        <h2>Test Sample Data</h2>
        <button onclick="initializeTestData()">Initialize Test Data</button>
        <div id="testDataResult"></div>
    </div>

    <div class="test-section">
        <h2>Test Filtering Functions</h2>
        <button onclick="testFilterAll()">Test Filter All</button>
        <button onclick="testFilterPending()">Test Filter Pending</button>
        <button onclick="testFilterApproved()">Test Filter Approved</button>
        <button onclick="testFilterRejected()">Test Filter Rejected</button>
        <div id="filterResult"></div>
    </div>

    <div class="test-section">
        <h2>Test Count Updates</h2>
        <button onclick="testCountUpdates()">Test Count Updates</button>
        <div id="countResult"></div>
    </div>

    <div class="test-section">
        <h2>Test Search with Filters</h2>
        <input type="text" id="testSearchInput" placeholder="Enter search term" value="john">
        <button onclick="testSearchWithFilter()">Test Search</button>
        <div id="searchResult"></div>
    </div>

    <script>
        // Mock data for testing
        let users = [];
        let filteredUsers = [];
        let currentApprovalFilter = 'all';

        // Mock functions from the main application
        function initializeTestData() {
            users = [
                { id: 1, username: 'john_doe', email: '<EMAIL>', name: 'John Doe', isApproved: 0 },
                { id: 2, username: 'jane_smith', email: '<EMAIL>', name: 'Jane Smith', isApproved: 1 },
                { id: 3, username: 'bob_wilson', email: '<EMAIL>', name: 'Bob Wilson', isApproved: 1 },
                { id: 4, username: 'alice_brown', email: '<EMAIL>', name: 'Alice Brown', isApproved: 0 },
                { id: 5, username: 'rejected_user', email: '<EMAIL>', name: 'Rejected User', isApproved: -1 },
                { id: 6, username: 'john_admin', email: '<EMAIL>', name: 'John Admin', isApproved: 1 }
            ];
            
            document.getElementById('testDataResult').innerHTML = 
                `<div class="test-result success">✓ Test data initialized: ${users.length} users created</div>`;
        }

        function applyCurrentFilters() {
            let filtered = [...users];
            
            // Apply approval status filter
            if (currentApprovalFilter !== 'all') {
                filtered = filtered.filter(user => {
                    switch (currentApprovalFilter) {
                        case 'pending':
                            return user.isApproved === 0;
                        case 'approved':
                            return user.isApproved === 1;
                        case 'rejected':
                            return user.isApproved === -1;
                        default:
                            return true;
                    }
                });
            }
            
            filteredUsers = filtered;
        }

        function updateApprovalCounts() {
            const counts = {
                all: users.length,
                pending: users.filter(user => user.isApproved === 0).length,
                approved: users.filter(user => user.isApproved === 1).length,
                rejected: users.filter(user => user.isApproved === -1).length
            };
            
            return counts;
        }

        function filterByApprovalStatus(status) {
            currentApprovalFilter = status;
            applyCurrentFilters();
            return filteredUsers;
        }

        // Test functions
        function testFilterAll() {
            if (users.length === 0) {
                document.getElementById('filterResult').innerHTML = 
                    '<div class="test-result error">✗ Please initialize test data first</div>';
                return;
            }

            const result = filterByApprovalStatus('all');
            const expected = users.length;
            const actual = result.length;
            
            if (actual === expected) {
                document.getElementById('filterResult').innerHTML = 
                    `<div class="test-result success">✓ Filter All: ${actual}/${expected} users shown</div>`;
            } else {
                document.getElementById('filterResult').innerHTML = 
                    `<div class="test-result error">✗ Filter All: Expected ${expected}, got ${actual}</div>`;
            }
        }

        function testFilterPending() {
            if (users.length === 0) {
                document.getElementById('filterResult').innerHTML = 
                    '<div class="test-result error">✗ Please initialize test data first</div>';
                return;
            }

            const result = filterByApprovalStatus('pending');
            const expected = users.filter(user => user.isApproved === 0).length;
            const actual = result.length;
            
            if (actual === expected) {
                document.getElementById('filterResult').innerHTML = 
                    `<div class="test-result success">✓ Filter Pending: ${actual}/${expected} users shown</div>`;
            } else {
                document.getElementById('filterResult').innerHTML = 
                    `<div class="test-result error">✗ Filter Pending: Expected ${expected}, got ${actual}</div>`;
            }
        }

        function testFilterApproved() {
            if (users.length === 0) {
                document.getElementById('filterResult').innerHTML = 
                    '<div class="test-result error">✗ Please initialize test data first</div>';
                return;
            }

            const result = filterByApprovalStatus('approved');
            const expected = users.filter(user => user.isApproved === 1).length;
            const actual = result.length;
            
            if (actual === expected) {
                document.getElementById('filterResult').innerHTML = 
                    `<div class="test-result success">✓ Filter Approved: ${actual}/${expected} users shown</div>`;
            } else {
                document.getElementById('filterResult').innerHTML = 
                    `<div class="test-result error">✗ Filter Approved: Expected ${expected}, got ${actual}</div>`;
            }
        }

        function testFilterRejected() {
            if (users.length === 0) {
                document.getElementById('filterResult').innerHTML = 
                    '<div class="test-result error">✗ Please initialize test data first</div>';
                return;
            }

            const result = filterByApprovalStatus('rejected');
            const expected = users.filter(user => user.isApproved === -1).length;
            const actual = result.length;
            
            if (actual === expected) {
                document.getElementById('filterResult').innerHTML = 
                    `<div class="test-result success">✓ Filter Rejected: ${actual}/${expected} users shown</div>`;
            } else {
                document.getElementById('filterResult').innerHTML = 
                    `<div class="test-result error">✗ Filter Rejected: Expected ${expected}, got ${actual}</div>`;
            }
        }

        function testCountUpdates() {
            if (users.length === 0) {
                document.getElementById('countResult').innerHTML = 
                    '<div class="test-result error">✗ Please initialize test data first</div>';
                return;
            }

            const counts = updateApprovalCounts();
            const expectedCounts = {
                all: 6,
                pending: 2,
                approved: 3,
                rejected: 1
            };

            let allCorrect = true;
            let resultHtml = '';

            for (const [status, count] of Object.entries(counts)) {
                if (count === expectedCounts[status]) {
                    resultHtml += `<div class="test-result success">✓ ${status}: ${count} users</div>`;
                } else {
                    resultHtml += `<div class="test-result error">✗ ${status}: Expected ${expectedCounts[status]}, got ${count}</div>`;
                    allCorrect = false;
                }
            }

            if (allCorrect) {
                resultHtml = '<div class="test-result success">✓ All counts are correct</div>' + resultHtml;
            }

            document.getElementById('countResult').innerHTML = resultHtml;
        }

        function testSearchWithFilter() {
            if (users.length === 0) {
                document.getElementById('searchResult').innerHTML = 
                    '<div class="test-result error">✗ Please initialize test data first</div>';
                return;
            }

            const searchTerm = document.getElementById('testSearchInput').value.toLowerCase();
            
            // Test search with 'approved' filter
            filterByApprovalStatus('approved');
            
            // Apply search to filtered results
            const searchResults = filteredUsers.filter(user =>
                user.username.toLowerCase().includes(searchTerm) ||
                user.email.toLowerCase().includes(searchTerm) ||
                user.name.toLowerCase().includes(searchTerm)
            );

            const expectedResults = users.filter(user => 
                user.isApproved === 1 && (
                    user.username.toLowerCase().includes(searchTerm) ||
                    user.email.toLowerCase().includes(searchTerm) ||
                    user.name.toLowerCase().includes(searchTerm)
                )
            );

            if (searchResults.length === expectedResults.length) {
                document.getElementById('searchResult').innerHTML = 
                    `<div class="test-result success">✓ Search with filter: Found ${searchResults.length} approved users matching "${searchTerm}"</div>`;
            } else {
                document.getElementById('searchResult').innerHTML = 
                    `<div class="test-result error">✗ Search with filter: Expected ${expectedResults.length}, got ${searchResults.length}</div>`;
            }
        }
    </script>
</body>
</html>
