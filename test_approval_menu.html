<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Approval Menu</title>
    <link rel="stylesheet" href="css/admin/admin-dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: rgb(11, 8, 16);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(30, 25, 40, 0.8);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .test-title {
            color: #1a7de8;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>User Management Approval Menu Test</h1>
        
        <div class="test-section">
            <h2 class="test-title">Approval Status Menu</h2>
            <div class="approval-menu">
                <div class="approval-tabs">
                    <button class="approval-tab active" data-status="all" onclick="testFilterByApprovalStatus('all')">
                        <i class="fas fa-users"></i>
                        <span>All Users</span>
                        <span class="tab-count" id="allUsersCount">25</span>
                    </button>
                    <button class="approval-tab" data-status="pending" onclick="testFilterByApprovalStatus('pending')">
                        <i class="fas fa-clock"></i>
                        <span>Pending Approval</span>
                        <span class="tab-count" id="pendingUsersCount">8</span>
                    </button>
                    <button class="approval-tab" data-status="approved" onclick="testFilterByApprovalStatus('approved')">
                        <i class="fas fa-check-circle"></i>
                        <span>Approved</span>
                        <span class="tab-count" id="approvedUsersCount">15</span>
                    </button>
                    <button class="approval-tab" data-status="rejected" onclick="testFilterByApprovalStatus('rejected')">
                        <i class="fas fa-times-circle"></i>
                        <span>Rejected</span>
                        <span class="tab-count" id="rejectedUsersCount">2</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">Sample User Cards</h2>
            <div class="users-grid">
                <!-- Pending User -->
                <div class="user-card">
                    <div class="user-badges">
                        <div class="pending-badge"><i class="fas fa-clock"></i> Pending</div>
                    </div>
                    <div class="user-card-header">
                        <div class="user-info">
                            <h3>john_doe</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="user-stats">
                        <div class="stat">
                            <div class="stat-value">0</div>
                            <div class="stat-label">Achievements</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">0</div>
                            <div class="stat-label">Levels</div>
                        </div>
                        <div class="stat exp-stat">
                            <div class="exp-level-indicator">
                                <span class="exp-level-text">1</span>
                            </div>
                            <div class="exp-bar-container">
                                <div class="exp-bar-fill" style="width: 0%"></div>
                            </div>
                            <div class="stat-label">Experience</div>
                        </div>
                    </div>
                </div>

                <!-- Approved User -->
                <div class="user-card">
                    <div class="user-badges">
                        <div class="approved-badge"><i class="fas fa-check"></i> Approved</div>
                    </div>
                    <div class="user-card-header">
                        <div class="user-info">
                            <h3>jane_smith</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="user-stats">
                        <div class="stat">
                            <div class="stat-value">12</div>
                            <div class="stat-label">Achievements</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">3</div>
                            <div class="stat-label">Levels</div>
                        </div>
                        <div class="stat exp-stat">
                            <div class="exp-level-indicator">
                                <span class="exp-level-text">5</span>
                            </div>
                            <div class="exp-bar-container">
                                <div class="exp-bar-fill" style="width: 60%"></div>
                            </div>
                            <div class="stat-label">Experience</div>
                        </div>
                    </div>
                </div>

                <!-- Rejected User -->
                <div class="user-card">
                    <div class="user-badges">
                        <div class="pending-badge" style="background-color: #dc3545;"><i class="fas fa-times"></i> Rejected</div>
                    </div>
                    <div class="user-card-header">
                        <div class="user-info">
                            <h3>rejected_user</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="user-stats">
                        <div class="stat">
                            <div class="stat-value">0</div>
                            <div class="stat-label">Achievements</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">0</div>
                            <div class="stat-label">Levels</div>
                        </div>
                        <div class="stat exp-stat">
                            <div class="exp-level-indicator">
                                <span class="exp-level-text">1</span>
                            </div>
                            <div class="exp-bar-container">
                                <div class="exp-bar-fill" style="width: 0%"></div>
                            </div>
                            <div class="stat-label">Experience</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">Test Results</h2>
            <div id="testResults">
                <p>Click on the approval status tabs above to test the functionality.</p>
            </div>
        </div>
    </div>

    <script>
        function testFilterByApprovalStatus(status) {
            // Update active tab
            document.querySelectorAll('.approval-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-status="${status}"]`).classList.add('active');
            
            // Update test results
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = `
                <p><strong>Filter Applied:</strong> ${status}</p>
                <p><strong>Status:</strong> <span style="color: #28a745;">✓ Tab switching works correctly</span></p>
                <p><strong>Active Tab:</strong> ${status.charAt(0).toUpperCase() + status.slice(1)} Users</p>
                <p><strong>Note:</strong> In the actual implementation, this would filter the user cards and update the display.</p>
            `;
        }
    </script>
</body>
</html>
