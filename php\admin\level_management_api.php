<?php
header('Content-Type: application/json');
require_once '../dbconnection.php';

class LevelManagementAPI {
    private $conn;
    private $gameDirectory;
    private $templateFiles;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
        $this->gameDirectory = '../../html/game/';
        $this->templateFiles = [
            'hangman' => 'hangman.html',
            'matching' => 'matching.html',
            'millionaire' => 'millionaire.html',
            'robot-battle' => 'robot_battle.html'
        ];
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';
        
        try {
            switch ($method) {
                case 'POST':
                    $this->handlePost($action);
                    break;
                case 'PUT':
                    $this->handlePut($action);
                    break;
                case 'DELETE':
                    $this->handleDelete($action);
                    break;
                case 'GET':
                    $this->handleGet($action);
                    break;
                default:
                    $this->sendResponse(['error' => 'Method not allowed'], 405);
            }
        } catch (Exception $e) {
            $this->sendResponse(['error' => $e->getMessage()], 500);
        }
    }
    
    private function handlePost($action) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if ($input === null) {
            $this->sendResponse(['error' => 'Invalid JSON input'], 400);
            return;
        }
        
        switch ($action) {
            case 'add_level':
                $this->addLevel($input);
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }

    private function handlePut($action) {
        $input = json_decode(file_get_contents('php://input'), true);

        if ($input === null) {
            $this->sendResponse(['error' => 'Invalid JSON input'], 400);
            return;
        }

        switch ($action) {
            case 'swap_levels':
                $this->swapLevels($input);
                break;
            case 'rearrange_levels':
                $this->rearrangeLevels($input);
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }
    
    private function handleDelete($action) {
        switch ($action) {
            case 'delete_level':
                $levelNumber = $_GET['level_number'] ?? 0;
                $this->deleteLevel($levelNumber);
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }
    
    private function handleGet($action) {
        switch ($action) {
            case 'get_next_level':
                $this->getNextLevelNumber();
                break;
            case 'get_existing_levels':
                $this->getExistingLevels();
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }
    
    private function swapLevels($data) {
        // Validate required fields
        if (!isset($data['level1']) || !isset($data['level2'])) {
            $this->sendResponse(['error' => 'Missing required fields: level1 and level2'], 400);
            return;
        }

        $level1 = intval($data['level1']);
        $level2 = intval($data['level2']);

        if ($level1 === $level2) {
            $this->sendResponse(['error' => 'Cannot swap a level with itself'], 400);
            return;
        }

        try {
            // Start transaction
            $this->conn->beginTransaction();

            // Check if both levels exist in database
            $stmt = $this->conn->prepare("SELECT COUNT(*) FROM game_content WHERE level_number IN (?, ?)");
            $stmt->execute([$level1, $level2]);
            $count = $stmt->fetchColumn();

            if ($count == 0) {
                throw new Exception('Neither level exists in database');
            }

            // Use a temporary level number to avoid constraint conflicts
            $tempLevel = 99999;

            // Move level1 to temp
            $stmt = $this->conn->prepare("UPDATE game_content SET level_number = ? WHERE level_number = ?");
            $stmt->execute([$tempLevel, $level1]);

            // Move level2 to level1
            $stmt = $this->conn->prepare("UPDATE game_content SET level_number = ? WHERE level_number = ?");
            $stmt->execute([$level1, $level2]);

            // Move temp to level2
            $stmt = $this->conn->prepare("UPDATE game_content SET level_number = ? WHERE level_number = ?");
            $stmt->execute([$level2, $tempLevel]);

            // Also swap user progress if exists
            $stmt = $this->conn->prepare("UPDATE user_levels SET level_number = ? WHERE level_number = ?");
            $stmt->execute([$tempLevel, $level1]);

            $stmt = $this->conn->prepare("UPDATE user_levels SET level_number = ? WHERE level_number = ?");
            $stmt->execute([$level1, $level2]);

            $stmt = $this->conn->prepare("UPDATE user_levels SET level_number = ? WHERE level_number = ?");
            $stmt->execute([$level2, $tempLevel]);

            // Swap the HTML files if they exist
            $file1 = $this->gameDirectory . "game{$level1}.html";
            $file2 = $this->gameDirectory . "game{$level2}.html";
            $tempFile = $this->gameDirectory . "temp_swap.html";

            $file1Exists = file_exists($file1);
            $file2Exists = file_exists($file2);

            if ($file1Exists && $file2Exists) {
                // Both files exist - swap them
                rename($file1, $tempFile);
                rename($file2, $file1);
                rename($tempFile, $file2);

                // Update data-level attributes in the swapped files
                $this->updateFileLevelAttribute($file1, $level1);
                $this->updateFileLevelAttribute($file2, $level2);
            } elseif ($file1Exists && !$file2Exists) {
                // Only file1 exists - move it to file2
                rename($file1, $file2);
                $this->updateFileLevelAttribute($file2, $level2);
            } elseif (!$file1Exists && $file2Exists) {
                // Only file2 exists - move it to file1
                rename($file2, $file1);
                $this->updateFileLevelAttribute($file1, $level1);
            }

            // Commit transaction
            $this->conn->commit();

            $this->sendResponse([
                'success' => true,
                'message' => "Levels {$level1} and {$level2} swapped successfully",
                'details' => [
                    'files_swapped' => $file1Exists || $file2Exists,
                    'database_updated' => true
                ]
            ]);

        } catch (Exception $e) {
            // Rollback transaction
            $this->conn->rollBack();
            $this->sendResponse(['error' => 'Failed to swap levels: ' . $e->getMessage()], 500);
        }
    }

    private function rearrangeLevels($data) {
        // Validate required fields
        if (!isset($data['level_mappings']) || !is_array($data['level_mappings'])) {
            $this->sendResponse(['error' => 'Missing or invalid level_mappings array'], 400);
            return;
        }

        $levelMappings = $data['level_mappings']; // Array of ['old_level' => new_level]

        try {
            // Start transaction
            $this->conn->beginTransaction();

            // Use temporary level numbers to avoid constraint conflicts
            $tempOffset = 100000;
            $processedFiles = [];

            // First pass: Move all levels to temporary numbers
            foreach ($levelMappings as $mapping) {
                if (!isset($mapping['old_level']) || !isset($mapping['new_level'])) {
                    throw new Exception('Invalid mapping format');
                }

                $oldLevel = intval($mapping['old_level']);
                $tempLevel = $tempOffset + $oldLevel;

                // Update database
                $stmt = $this->conn->prepare("UPDATE game_content SET level_number = ? WHERE level_number = ?");
                $stmt->execute([$tempLevel, $oldLevel]);

                $stmt = $this->conn->prepare("UPDATE user_levels SET level_number = ? WHERE level_number = ?");
                $stmt->execute([$tempLevel, $oldLevel]);

                // Handle file renaming to temp
                $oldFile = $this->gameDirectory . "game{$oldLevel}.html";
                $tempFile = $this->gameDirectory . "temp{$oldLevel}.html";

                if (file_exists($oldFile)) {
                    rename($oldFile, $tempFile);
                    $processedFiles[$oldLevel] = $tempFile;
                }
            }

            // Second pass: Move from temporary numbers to final positions
            foreach ($levelMappings as $mapping) {
                $oldLevel = intval($mapping['old_level']);
                $newLevel = intval($mapping['new_level']);
                $tempLevel = $tempOffset + $oldLevel;

                // Update database
                $stmt = $this->conn->prepare("UPDATE game_content SET level_number = ? WHERE level_number = ?");
                $stmt->execute([$newLevel, $tempLevel]);

                $stmt = $this->conn->prepare("UPDATE user_levels SET level_number = ? WHERE level_number = ?");
                $stmt->execute([$newLevel, $tempLevel]);

                // Handle file renaming to final position
                if (isset($processedFiles[$oldLevel])) {
                    $tempFile = $processedFiles[$oldLevel];
                    $newFile = $this->gameDirectory . "game{$newLevel}.html";

                    rename($tempFile, $newFile);
                    $this->updateFileLevelAttribute($newFile, $newLevel);
                }
            }

            // Commit transaction
            $this->conn->commit();

            $this->sendResponse([
                'success' => true,
                'message' => 'Levels rearranged successfully',
                'processed_mappings' => count($levelMappings)
            ]);

        } catch (Exception $e) {
            // Rollback transaction
            $this->conn->rollBack();
            $this->sendResponse(['error' => 'Failed to rearrange levels: ' . $e->getMessage()], 500);
        }
    }

    private function updateFileLevelAttribute($filePath, $levelNumber) {
        if (!file_exists($filePath)) {
            return;
        }

        $content = file_get_contents($filePath);
        $content = preg_replace('/data-level="[^"]*"/', "data-level=\"{$levelNumber}\"", $content);
        file_put_contents($filePath, $content);
    }

    private function addLevel($data) {
        // Validate required fields
        if (!isset($data['game_category']) || !isset($data['level_number'])) {
            $this->sendResponse(['error' => 'Missing required fields: game_category and level_number'], 400);
            return;
        }
        
        $gameCategory = $data['game_category'];
        $levelNumber = intval($data['level_number']);
        
        // Validate game category
        if (!isset($this->templateFiles[$gameCategory])) {
            $this->sendResponse(['error' => 'Invalid game category'], 400);
            return;
        }
        
        // Check if level already exists
        $targetFile = $this->gameDirectory . "game{$levelNumber}.html";
        if (file_exists($targetFile)) {
            $this->sendResponse(['error' => "Level {$levelNumber} already exists"], 400);
            return;
        }
        
        // Get template file path
        $templateFile = $this->gameDirectory . $this->templateFiles[$gameCategory];
        if (!file_exists($templateFile)) {
            $this->sendResponse(['error' => 'Template file not found'], 404);
            return;
        }
        
        try {
            // Start transaction
            $this->conn->beginTransaction();

            // Read template content
            $templateContent = file_get_contents($templateFile);

            // Modify the content for the new level
            $modifiedContent = $this->modifyTemplateContent($templateContent, $levelNumber, $gameCategory);

            // Write to new file
            if (file_put_contents($targetFile, $modifiedContent) === false) {
                throw new Exception('Failed to create level file');
            }

            // Insert blank record into game_content table
            $stmt = $this->conn->prepare("INSERT INTO game_content (level_number, question_text, option1, option2, option3, option4, correct_answer, can_edit, quiz_type) VALUES (?, '', '', '', '', '', '', 1, 'Multiple Choice')");
            $stmt->execute([$levelNumber]);

            // Commit transaction
            $this->conn->commit();

            $this->sendResponse([
                'success' => true,
                'message' => "Level {$levelNumber} created successfully with blank question record",
                'file' => "game{$levelNumber}.html"
            ]);

        } catch (Exception $e) {
            // Rollback transaction
            if ($this->conn->inTransaction()) {
                $this->conn->rollBack();
            }

            // Clean up file if it was created
            if (file_exists($targetFile)) {
                unlink($targetFile);
            }

            $this->sendResponse(['error' => 'Failed to create level: ' . $e->getMessage()], 500);
        }
    }
    
    private function modifyTemplateContent($content, $levelNumber, $gameCategory) {
        // Update the data-level attribute in body tag
        $content = preg_replace('/data-level="[^"]*"/', "data-level=\"{$levelNumber}\"", $content);

        // Update CSS and JS links to use relative paths for game files
        $content = str_replace('href="../css/', 'href="../../css/', $content);
        $content = str_replace('src="../js/', 'src="../../js/', $content);
        $content = str_replace('src="../images/', 'src="../../images/', $content);

        // Update specific game category modifications
        switch ($gameCategory) {
            case 'hangman':
                $content = str_replace('Robot Rescue Quiz', "Robot Rescue Quiz - Level {$levelNumber}", $content);
                break;
            case 'matching':
                $content = str_replace('Matching Game - FunConnect', "Matching Game - Level {$levelNumber}", $content);
                break;
            case 'millionaire':
                $content = str_replace('Who Wants to Be a Millionaire - Robot Edition', "Millionaire Quiz - Level {$levelNumber}", $content);
                break;
            case 'robot-battle':
                $content = str_replace('Robot Battle', "Robot Battle - Level {$levelNumber}", $content);
                break;
        }

        return $content;
    }
    
    private function deleteLevel($levelNumber) {
        if (!$levelNumber || !is_numeric($levelNumber)) {
            $this->sendResponse(['error' => 'Invalid level number'], 400);
            return;
        }
        
        $levelNumber = intval($levelNumber);
        $targetFile = $this->gameDirectory . "game{$levelNumber}.html";
        
        try {
            // Start transaction
            $this->conn->beginTransaction();
            
            // Delete all questions for this level from database
            $stmt = $this->conn->prepare("DELETE FROM game_content WHERE level_number = ?");
            $stmt->execute([$levelNumber]);
            $deletedQuestions = $stmt->rowCount();
            
            // Delete user progress for this level
            $stmt = $this->conn->prepare("DELETE FROM user_levels WHERE level_number = ?");
            $stmt->execute([$levelNumber]);
            $deletedUserProgress = $stmt->rowCount();
            
            // Delete the file if it exists
            $fileDeleted = false;
            if (file_exists($targetFile)) {
                if (unlink($targetFile)) {
                    $fileDeleted = true;
                } else {
                    throw new Exception('Failed to delete level file');
                }
            }
            
            // Commit transaction
            $this->conn->commit();
            
            $this->sendResponse([
                'success' => true,
                'message' => "Level {$levelNumber} deleted successfully",
                'details' => [
                    'file_deleted' => $fileDeleted,
                    'questions_deleted' => $deletedQuestions,
                    'user_progress_deleted' => $deletedUserProgress
                ]
            ]);
            
        } catch (Exception $e) {
            // Rollback transaction
            $this->conn->rollBack();
            $this->sendResponse(['error' => 'Failed to delete level: ' . $e->getMessage()], 500);
        }
    }
    
    private function getNextLevelNumber() {
        try {
            // Get highest level number from database
            $stmt = $this->conn->prepare("SELECT MAX(level_number) as max_level FROM game_content");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $nextLevel = ($result['max_level'] ?? 0) + 1;
            
            $this->sendResponse([
                'success' => true,
                'next_level' => $nextLevel
            ]);
            
        } catch (Exception $e) {
            $this->sendResponse(['error' => 'Failed to get next level number: ' . $e->getMessage()], 500);
        }
    }
    
    private function getExistingLevels() {
        try {
            // Get all existing level files
            $files = glob($this->gameDirectory . 'game*.html');
            $levels = [];
            
            foreach ($files as $file) {
                if (preg_match('/game(\d+)\.html$/', basename($file), $matches)) {
                    $levels[] = intval($matches[1]);
                }
            }
            
            sort($levels);
            
            $this->sendResponse([
                'success' => true,
                'existing_levels' => $levels
            ]);
            
        } catch (Exception $e) {
            $this->sendResponse(['error' => 'Failed to get existing levels: ' . $e->getMessage()], 500);
        }
    }
    
    private function sendResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }
}

// Initialize and handle the request
$api = new LevelManagementAPI();
$api->handleRequest();
?>
