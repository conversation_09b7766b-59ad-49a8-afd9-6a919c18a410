<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dropdown Styling Test</title>
    <link rel="stylesheet" href="css/admin/admin-dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: rgb(11, 8, 16);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .test-title {
            color: #1a7de8;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(26, 125, 232, 0.5);
        }
        .test-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.2rem;
        }
        .feature-highlight {
            background: rgba(39, 174, 96, 0.1);
            border: 1px solid rgba(39, 174, 96, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
        }
        .feature-list li i {
            color: #27ae60;
            width: 20px;
        }
        .demo-section {
            margin-bottom: 40px;
        }
        .section-title {
            color: #fff;
            font-size: 1.8rem;
            margin-bottom: 20px;
            text-align: center;
        }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        .demo-card {
            background: rgba(30, 25, 40, 0.8);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .demo-card h3 {
            color: #1a7de8;
            margin-bottom: 15px;
        }
        .status-indicator {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 0.8rem;
            font-weight: 600;
            margin: 5px;
        }
        .status-updated { background: linear-gradient(135deg, #3498db, #2980b9); }
        .status-automated { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
        .status-enhanced { background: linear-gradient(135deg, #27ae60, #229954); }
        .year-display {
            background: rgba(26, 125, 232, 0.1);
            border: 1px solid rgba(26, 125, 232, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            text-align: center;
        }
        .current-year {
            font-size: 1.5rem;
            font-weight: bold;
            color: #1a7de8;
            margin-bottom: 10px;
        }
        .school-years {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 10px;
        }
        .school-year-option {
            background: rgba(39, 174, 96, 0.2);
            border: 1px solid rgba(39, 174, 96, 0.4);
            border-radius: 6px;
            padding: 8px 16px;
            color: #27ae60;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">Dropdown Styling & Automation</h1>
            <p class="test-subtitle">Enhanced dropdown appearance and automated school year options</p>
        </div>

        <div class="feature-highlight">
            <h3 style="color: #27ae60; margin-bottom: 15px;">Improvements Implemented</h3>
            <ul class="feature-list">
                <li><i class="fas fa-check"></i> Darkened dropdown background color for better contrast</li>
                <li><i class="fas fa-check"></i> Enhanced font color to light/bright for better readability</li>
                <li><i class="fas fa-check"></i> Automated school year options based on current year</li>
                <li><i class="fas fa-check"></i> Reduced options to only 2 relevant school years</li>
                <li><i class="fas fa-check"></i> Applied to all semester and school year dropdowns</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2 class="section-title">Current Year & School Year Options</h2>
            
            <div class="year-display">
                <div class="current-year" id="currentYearDisplay">Current Year: 2024</div>
                <p style="color: rgba(255, 255, 255, 0.8);">Based on today's date, the system automatically generates:</p>
                <div class="school-years">
                    <div class="school-year-option" id="prevSchoolYear">2023-2024</div>
                    <div class="school-year-option" id="currentSchoolYear">2024-2025</div>
                </div>
                <p style="color: rgba(255, 255, 255, 0.6); margin-top: 15px; font-size: 0.9rem;">
                    Only 2 options are shown: Previous academic year and Current academic year
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">Enhanced Dropdown Styling</h2>
            <div class="demo-grid">
                
                <div class="demo-card">
                    <h3>Bulk Approval Form</h3>
                    <div class="form-group">
                        <label for="testBulkSemester">Semester:</label>
                        <select id="testBulkSemester" name="semester">
                            <option value="">Select Semester</option>
                            <option value="1st Semester">1st Semester</option>
                            <option value="2nd Semester">2nd Semester</option>
                            <option value="Summer">Summer</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="testBulkSchoolyr">School Year:</label>
                        <select id="testBulkSchoolyr" name="schoolyr">
                            <option value="">Select School Year</option>
                        </select>
                    </div>
                    <div class="status-indicator status-updated">✓ Updated: Dark Background</div>
                </div>

                <div class="demo-card">
                    <h3>Edit User Form</h3>
                    <div class="form-group">
                        <label for="testEditSemester">Semester:</label>
                        <select id="testEditSemester" name="semester">
                            <option value="">Select Semester</option>
                            <option value="1st Semester">1st Semester</option>
                            <option value="2nd Semester">2nd Semester</option>
                            <option value="Summer">Summer</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="testEditSchoolyr">School Year:</label>
                        <select id="testEditSchoolyr" name="schoolyr">
                            <option value="">Select School Year</option>
                        </select>
                    </div>
                    <div class="status-indicator status-automated">✓ Automated: 2 Options Only</div>
                </div>

                <div class="demo-card">
                    <h3>Individual Approval Form</h3>
                    <div class="form-group">
                        <label for="testApprovalSemester">Semester:</label>
                        <select id="testApprovalSemester" name="semester">
                            <option value="">Select Semester</option>
                            <option value="1st Semester">1st Semester</option>
                            <option value="2nd Semester">2nd Semester</option>
                            <option value="Summer">Summer</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="testApprovalSchoolyr">School Year:</label>
                        <select id="testApprovalSchoolyr" name="schoolyr">
                            <option value="">Select School Year</option>
                        </select>
                    </div>
                    <div class="status-indicator status-enhanced">✓ Enhanced: Light Font Color</div>
                </div>

                <div class="demo-card">
                    <h3>Advanced Filter</h3>
                    <div class="filter-control-group">
                        <label for="testSchoolYearFilter" class="filter-label">School Year Filter:</label>
                        <select id="testSchoolYearFilter" class="advanced-filter-select">
                            <option value="all">All School Years</option>
                        </select>
                    </div>
                    <div style="margin-top: 15px;">
                        <div class="status-indicator status-automated">✓ Automated: Current Year Based</div>
                    </div>
                </div>

            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">Styling Improvements</h2>
            <div style="background: rgba(30, 25, 40, 0.8); border-radius: 12px; padding: 20px;">
                <h4 style="color: #1a7de8; margin-bottom: 15px;">CSS Changes Applied:</h4>
                <div class="demo-grid">
                    <div>
                        <h5 style="color: #3498db;">Background Color:</h5>
                        <ul style="color: rgba(255, 255, 255, 0.8); line-height: 1.6;">
                            <li>✓ Changed from rgba(255, 255, 255, 0.05)</li>
                            <li>✓ To rgba(15, 12, 20, 0.95)</li>
                            <li>✓ Much darker for better contrast</li>
                        </ul>
                    </div>
                    <div>
                        <h5 style="color: #27ae60;">Font Color:</h5>
                        <ul style="color: rgba(255, 255, 255, 0.8); line-height: 1.6;">
                            <li>✓ Enhanced to rgba(255, 255, 255, 0.95)</li>
                            <li>✓ Added font-weight: 500</li>
                            <li>✓ Better readability on dark background</li>
                        </ul>
                    </div>
                    <div>
                        <h5 style="color: #9b59b6;">Automation Logic:</h5>
                        <ul style="color: rgba(255, 255, 255, 0.8); line-height: 1.6;">
                            <li>✓ Current year - 1 to current year</li>
                            <li>✓ Current year to current year + 1</li>
                            <li>✓ Only 2 relevant options shown</li>
                        </ul>
                    </div>
                    <div>
                        <h5 style="color: #e67e22;">Benefits:</h5>
                        <ul style="color: rgba(255, 255, 255, 0.8); line-height: 1.6;">
                            <li>✓ Better visual contrast</li>
                            <li>✓ Reduced option clutter</li>
                            <li>✓ Always current and relevant</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize school year options for demo (only 2 options based on current year)
        function initializeDemoSchoolYears() {
            const currentYear = new Date().getFullYear();
            const schoolYearSelects = ['testBulkSchoolyr', 'testEditSchoolyr', 'testApprovalSchoolyr', 'testSchoolYearFilter'];
            
            // Update display
            document.getElementById('currentYearDisplay').textContent = `Current Year: ${currentYear}`;
            document.getElementById('prevSchoolYear').textContent = `${currentYear - 1}-${currentYear}`;
            document.getElementById('currentSchoolYear').textContent = `${currentYear}-${currentYear + 1}`;
            
            schoolYearSelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    // Clear existing options except the first one
                    while (select.children.length > 1) {
                        select.removeChild(select.lastChild);
                    }
                    
                    // Add only 2 school year options based on current year
                    // Option 1: Previous year to current year
                    const prevSchoolYear = `${currentYear - 1}-${currentYear}`;
                    const option1 = document.createElement('option');
                    option1.value = prevSchoolYear;
                    option1.textContent = prevSchoolYear;
                    select.appendChild(option1);
                    
                    // Option 2: Current year to next year
                    const currentSchoolYear = `${currentYear}-${currentYear + 1}`;
                    const option2 = document.createElement('option');
                    option2.value = currentSchoolYear;
                    option2.textContent = currentSchoolYear;
                    
                    // Set current school year as default only for form selects, not for filter
                    if (selectId !== 'testSchoolYearFilter') {
                        option2.selected = true;
                    }
                    
                    select.appendChild(option2);
                }
            });
        }

        // Initialize on load
        document.addEventListener('DOMContentLoaded', function() {
            initializeDemoSchoolYears();
        });

        // Add interactive effects for demonstration
        document.querySelectorAll('select').forEach(select => {
            select.addEventListener('change', function() {
                console.log(`Selected: ${this.value} in ${this.id}`);
                
                // Visual feedback
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
