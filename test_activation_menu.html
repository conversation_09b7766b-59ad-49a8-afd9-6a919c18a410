<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activation Menu Test</title>
    <link rel="stylesheet" href="css/admin/admin-dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: rgb(11, 8, 16);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .test-title {
            color: #1a7de8;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(26, 125, 232, 0.5);
        }
        .test-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.2rem;
        }
        .feature-highlight {
            background: rgba(39, 174, 96, 0.1);
            border: 1px solid rgba(39, 174, 96, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
        }
        .feature-list li i {
            color: #27ae60;
            width: 20px;
        }
        .demo-section {
            margin-bottom: 40px;
        }
        .section-title {
            color: #fff;
            font-size: 1.8rem;
            margin-bottom: 20px;
            text-align: center;
        }
        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 24px;
            margin-bottom: 30px;
        }
        .demo-status {
            background: rgba(30, 25, 40, 0.8);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        .status-indicator {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            margin: 0 10px;
        }
        .approval-pending { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .approval-approved { background: linear-gradient(135deg, #27ae60, #229954); }
        .activation-active { background: linear-gradient(135deg, #3498db, #2980b9); }
        .activation-deactivated { background: linear-gradient(135deg, #e74c3c, #c0392b); }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">Activation Menu System</h1>
            <p class="test-subtitle">Dual-filter system with bulk activation/deactivation operations</p>
        </div>

        <div class="feature-highlight">
            <h3 style="color: #27ae60; margin-bottom: 15px;">New Activation Features</h3>
            <ul class="feature-list">
                <li><i class="fas fa-check"></i> Separate activation status filtering (Active/Deactivated)</li>
                <li><i class="fas fa-check"></i> Dual-filter system (Approval + Activation status)</li>
                <li><i class="fas fa-check"></i> Bulk activate/deactivate operations with multiple selection</li>
                <li><i class="fas fa-check"></i> Context-aware bulk operations based on current filters</li>
                <li><i class="fas fa-check"></i> Real-time count updates for both filter types</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2 class="section-title">Enhanced Filter Menu</h2>
            
            <!-- Enhanced Filter Menu -->
            <div class="filter-menu">
                <div class="filter-section">
                    <h3 class="filter-title">Filter by Approval Status</h3>
                    <div class="approval-tabs">
                        <button class="approval-tab active" data-status="all">
                            <i class="fas fa-users"></i>
                            <span>All Users</span>
                            <span class="tab-count">25</span>
                        </button>
                        <button class="approval-tab" data-status="pending">
                            <i class="fas fa-clock"></i>
                            <span>Pending Approval</span>
                            <span class="tab-count">8</span>
                        </button>
                        <button class="approval-tab" data-status="approved">
                            <i class="fas fa-check-circle"></i>
                            <span>Approved</span>
                            <span class="tab-count">15</span>
                        </button>
                        <button class="approval-tab" data-status="rejected">
                            <i class="fas fa-times-circle"></i>
                            <span>Rejected</span>
                            <span class="tab-count">2</span>
                        </button>
                    </div>
                </div>

                <div class="filter-section">
                    <h3 class="filter-title">Filter by Account Status</h3>
                    <div class="activation-tabs">
                        <button class="activation-tab active" data-activation="all">
                            <i class="fas fa-users-cog"></i>
                            <span>All Accounts</span>
                            <span class="tab-count">25</span>
                        </button>
                        <button class="activation-tab" data-activation="active">
                            <i class="fas fa-user-check"></i>
                            <span>Active</span>
                            <span class="tab-count">20</span>
                        </button>
                        <button class="activation-tab" data-activation="deactivated">
                            <i class="fas fa-user-slash"></i>
                            <span>Deactivated</span>
                            <span class="tab-count">5</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Enhanced Bulk Operations -->
            <div class="bulk-operations" style="display: block;">
                <div class="bulk-controls">
                    <span>3 users selected</span>
                    
                    <!-- Approval Operations -->
                    <div class="bulk-group approval-operations">
                        <button class="bulk-btn approve-btn">
                            <i class="fas fa-check"></i> Bulk Approve
                        </button>
                        <button class="bulk-btn reject-btn" style="display: none;">
                            <i class="fas fa-times"></i> Bulk Disapprove
                        </button>
                    </div>

                    <!-- Activation Operations -->
                    <div class="bulk-group activation-operations">
                        <button class="bulk-btn activate-btn" style="display: none;">
                            <i class="fas fa-user-check"></i> Bulk Activate
                        </button>
                        <button class="bulk-btn deactivate-btn">
                            <i class="fas fa-user-slash"></i> Bulk Deactivate
                        </button>
                    </div>

                    <button class="bulk-btn clear-btn">
                        <i class="fas fa-times-circle"></i> Clear Selection
                    </button>
                </div>
            </div>

            <div class="demo-status">
                <h3 style="color: #1a7de8; margin-bottom: 15px;">Current Filter Status</h3>
                <div>
                    <span class="status-indicator approval-approved">Approval: Approved</span>
                    <span class="status-indicator activation-active">Activation: Active</span>
                </div>
                <p style="margin-top: 15px; color: rgba(255, 255, 255, 0.7);">
                    Showing approved and active users with bulk deactivate option available
                </p>
            </div>

            <div class="users-grid">
                
                <!-- Sample User Card with Checkboxes -->
                <div class="user-card">
                    <div class="user-selection">
                        <input type="checkbox" class="user-checkbox" checked>
                    </div>

                    <div class="user-profile-section">
                        <div class="user-avatar">
                            <img src="images/profile-icon.png" alt="active_user">
                            <div class="user-level-badge">5</div>
                        </div>
                        <div class="user-basic-info">
                            <h3 class="user-name">active_user</h3>
                            <p class="user-email"><EMAIL></p>
                            <div class="user-meta">
                                <span class="join-date">
                                    <i class="fas fa-calendar-alt"></i>
                                    Joined Nov 15, 2024
                                </span>
                                <span class="full-name">
                                    <i class="fas fa-user"></i>
                                    Active User
                                </span>
                                <div class="user-status-info">
                                    <div class="status-badge approved-badge">
                                        <i class="fas fa-check-circle"></i>
                                        <span>Approved</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="user-progress-stats">
                        <div class="progress-item">
                            <div class="progress-icon achievements-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">15/50</div>
                                <div class="progress-label">Achievements</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 30%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="progress-item">
                            <div class="progress-icon levels-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">4/6</div>
                                <div class="progress-label">Levels Completed</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 67%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="progress-item experience-item">
                            <div class="progress-icon experience-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="progress-info">
                                <div class="progress-value">750/1000 EXP</div>
                                <div class="progress-label">Experience Level 5</div>
                                <div class="progress-bar experience-bar">
                                    <div class="progress-fill experience-fill" style="width: 75%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="user-quick-actions">
                        <button class="quick-action-btn view-btn">
                            <i class="fas fa-eye"></i>
                            <span>View</span>
                        </button>
                        <button class="quick-action-btn edit-btn">
                            <i class="fas fa-edit"></i>
                            <span>Edit</span>
                        </button>
                        <button class="quick-action-btn deactivate-btn">
                            <i class="fas fa-user-slash"></i>
                            <span>Deactivate</span>
                        </button>
                    </div>
                </div>

            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">Filter Combinations</h2>
            <div style="background: rgba(30, 25, 40, 0.8); border-radius: 12px; padding: 20px;">
                <h4 style="color: #27ae60; margin-bottom: 15px;">Possible Filter Combinations:</h4>
                <ul style="color: rgba(255, 255, 255, 0.8); line-height: 1.8;">
                    <li><strong>Pending + Active:</strong> New users awaiting approval who can access the system</li>
                    <li><strong>Pending + Deactivated:</strong> New users awaiting approval who are temporarily blocked</li>
                    <li><strong>Approved + Active:</strong> Fully approved and active users (normal state)</li>
                    <li><strong>Approved + Deactivated:</strong> Approved users who have been temporarily suspended</li>
                    <li><strong>Rejected + Active:</strong> Rejected users who still have system access (unusual)</li>
                    <li><strong>Rejected + Deactivated:</strong> Rejected and blocked users</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Add interactivity for demonstration
        document.querySelectorAll('.approval-tab, .activation-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from siblings
                const siblings = this.parentElement.querySelectorAll('.approval-tab, .activation-tab');
                siblings.forEach(sibling => sibling.classList.remove('active'));
                
                // Add active class to clicked tab
                this.classList.add('active');
                
                // Update demo status
                updateDemoStatus();
            });
        });

        function updateDemoStatus() {
            const activeApproval = document.querySelector('.approval-tab.active').dataset.status;
            const activeActivation = document.querySelector('.activation-tab.active').dataset.activation;
            
            const statusDiv = document.querySelector('.demo-status');
            const approvalSpan = statusDiv.querySelector('.status-indicator:first-child');
            const activationSpan = statusDiv.querySelector('.status-indicator:last-child');
            const description = statusDiv.querySelector('p');
            
            // Update approval status
            approvalSpan.textContent = `Approval: ${activeApproval.charAt(0).toUpperCase() + activeApproval.slice(1)}`;
            approvalSpan.className = `status-indicator approval-${activeApproval === 'all' ? 'approved' : activeApproval}`;
            
            // Update activation status
            activationSpan.textContent = `Activation: ${activeActivation.charAt(0).toUpperCase() + activeActivation.slice(1)}`;
            activationSpan.className = `status-indicator activation-${activeActivation === 'all' ? 'active' : activeActivation}`;
            
            // Update description
            let desc = `Showing ${activeApproval} users who are ${activeActivation}`;
            if (activeApproval === 'all' && activeActivation === 'all') {
                desc = 'Showing all users regardless of approval or activation status';
            }
            description.textContent = desc;
        }

        // Add checkbox functionality
        document.querySelectorAll('.user-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const userCard = this.closest('.user-card');
                if (this.checked) {
                    userCard.classList.add('selected');
                } else {
                    userCard.classList.remove('selected');
                }
            });
        });
    </script>
</body>
</html>
