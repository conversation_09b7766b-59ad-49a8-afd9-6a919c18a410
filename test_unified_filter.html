<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unified Filter Container Test</title>
    <link rel="stylesheet" href="css/admin/admin-dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: rgb(11, 8, 16);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .test-title {
            color: #1a7de8;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(26, 125, 232, 0.5);
        }
        .test-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.2rem;
        }
        .feature-highlight {
            background: rgba(26, 125, 232, 0.1);
            border: 1px solid rgba(26, 125, 232, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
        }
        .feature-list li i {
            color: #1a7de8;
            width: 20px;
        }
        .demo-section {
            margin-bottom: 40px;
        }
        .section-title {
            color: #fff;
            font-size: 1.8rem;
            margin-bottom: 20px;
            text-align: center;
        }
        .comparison-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .demo-status {
            background: rgba(30, 25, 40, 0.8);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        .status-indicator {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            margin: 0 10px;
        }
        .approval-all { background: linear-gradient(135deg, #1a7de8, #1565C0); }
        .approval-pending { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .approval-approved { background: linear-gradient(135deg, #27ae60, #229954); }
        .approval-rejected { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .activation-all { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
        .activation-active { background: linear-gradient(135deg, #3498db, #2980b9); }
        .activation-deactivated { background: linear-gradient(135deg, #e67e22, #d35400); }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">Unified Filter Container</h1>
            <p class="test-subtitle">Combined approval and activation status filters in one cohesive interface</p>
        </div>

        <div class="feature-highlight">
            <h3 style="color: #1a7de8; margin-bottom: 15px;">Unified Filter Benefits</h3>
            <ul class="feature-list">
                <li><i class="fas fa-check"></i> Single container for both approval and activation filters</li>
                <li><i class="fas fa-check"></i> Better visual hierarchy with grouped filter sections</li>
                <li><i class="fas fa-check"></i> Reduced visual clutter and improved organization</li>
                <li><i class="fas fa-check"></i> Consistent styling and spacing throughout</li>
                <li><i class="fas fa-check"></i> Enhanced mobile responsiveness</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2 class="section-title">Unified Filter Interface</h2>
            
            <!-- Unified Filter Menu -->
            <div class="unified-filter-menu">
                <div class="filter-container">
                    <h3 class="main-filter-title">
                        <i class="fas fa-filter"></i>
                        User Filters
                    </h3>
                    
                    <div class="filter-groups">
                        <!-- Approval Status Filters -->
                        <div class="filter-group">
                            <h4 class="filter-group-title">Approval Status</h4>
                            <div class="filter-tabs approval-tabs">
                                <button class="filter-tab approval-tab active" data-status="all" onclick="updateFilter('approval', 'all')">
                                    <i class="fas fa-users"></i>
                                    <span>All Users</span>
                                    <span class="tab-count">25</span>
                                </button>
                                <button class="filter-tab approval-tab" data-status="pending" onclick="updateFilter('approval', 'pending')">
                                    <i class="fas fa-clock"></i>
                                    <span>Pending</span>
                                    <span class="tab-count">8</span>
                                </button>
                                <button class="filter-tab approval-tab" data-status="approved" onclick="updateFilter('approval', 'approved')">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Approved</span>
                                    <span class="tab-count">15</span>
                                </button>
                                <button class="filter-tab approval-tab" data-status="rejected" onclick="updateFilter('approval', 'rejected')">
                                    <i class="fas fa-times-circle"></i>
                                    <span>Rejected</span>
                                    <span class="tab-count">2</span>
                                </button>
                            </div>
                        </div>

                        <!-- Account Status Filters -->
                        <div class="filter-group">
                            <h4 class="filter-group-title">Account Status</h4>
                            <div class="filter-tabs activation-tabs">
                                <button class="filter-tab activation-tab active" data-activation="all" onclick="updateFilter('activation', 'all')">
                                    <i class="fas fa-users-cog"></i>
                                    <span>All Accounts</span>
                                    <span class="tab-count">25</span>
                                </button>
                                <button class="filter-tab activation-tab" data-activation="active" onclick="updateFilter('activation', 'active')">
                                    <i class="fas fa-user-check"></i>
                                    <span>Active</span>
                                    <span class="tab-count">20</span>
                                </button>
                                <button class="filter-tab activation-tab" data-activation="deactivated" onclick="updateFilter('activation', 'deactivated')">
                                    <i class="fas fa-user-slash"></i>
                                    <span>Deactivated</span>
                                    <span class="tab-count">5</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Bulk Operations -->
            <div class="bulk-operations" style="display: block;">
                <div class="bulk-controls">
                    <span>3 users selected</span>
                    
                    <!-- Approval Operations -->
                    <div class="bulk-group approval-operations">
                        <button class="bulk-btn approve-btn">
                            <i class="fas fa-check"></i> Bulk Approve
                        </button>
                        <button class="bulk-btn reject-btn" style="display: none;">
                            <i class="fas fa-times"></i> Bulk Disapprove
                        </button>
                    </div>

                    <!-- Activation Operations -->
                    <div class="bulk-group activation-operations">
                        <button class="bulk-btn activate-btn" style="display: none;">
                            <i class="fas fa-user-check"></i> Bulk Activate
                        </button>
                        <button class="bulk-btn deactivate-btn">
                            <i class="fas fa-user-slash"></i> Bulk Deactivate
                        </button>
                    </div>

                    <button class="bulk-btn clear-btn">
                        <i class="fas fa-times-circle"></i> Clear Selection
                    </button>
                </div>
            </div>

            <div class="demo-status">
                <h3 style="color: #1a7de8; margin-bottom: 15px;">Current Filter Status</h3>
                <div id="statusIndicators">
                    <span class="status-indicator approval-all">Approval: All Users</span>
                    <span class="status-indicator activation-all">Activation: All Accounts</span>
                </div>
                <p style="margin-top: 15px; color: rgba(255, 255, 255, 0.7);" id="statusDescription">
                    Showing all users regardless of approval or activation status
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">Design Improvements</h2>
            <div style="background: rgba(30, 25, 40, 0.8); border-radius: 12px; padding: 20px;">
                <h4 style="color: #1a7de8; margin-bottom: 15px;">Key Improvements in Unified Design:</h4>
                <ul style="color: rgba(255, 255, 255, 0.8); line-height: 1.8;">
                    <li><strong>Single Container:</strong> Both filter types are now contained in one cohesive interface</li>
                    <li><strong>Visual Hierarchy:</strong> Clear main title with grouped filter sections underneath</li>
                    <li><strong>Consistent Styling:</strong> Unified design language across all filter elements</li>
                    <li><strong>Better Organization:</strong> Logical grouping of related filter options</li>
                    <li><strong>Space Efficiency:</strong> More compact layout without sacrificing functionality</li>
                    <li><strong>Enhanced Readability:</strong> Improved typography and spacing for better user experience</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let currentApproval = 'all';
        let currentActivation = 'all';

        function updateFilter(type, value) {
            if (type === 'approval') {
                currentApproval = value;
                // Update approval tabs
                document.querySelectorAll('.approval-tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelector(`[data-status="${value}"]`).classList.add('active');
            } else if (type === 'activation') {
                currentActivation = value;
                // Update activation tabs
                document.querySelectorAll('.activation-tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelector(`[data-activation="${value}"]`).classList.add('active');
            }
            
            updateStatusDisplay();
        }

        function updateStatusDisplay() {
            const statusIndicators = document.getElementById('statusIndicators');
            const statusDescription = document.getElementById('statusDescription');
            
            // Update approval indicator
            const approvalSpan = statusIndicators.children[0];
            approvalSpan.textContent = `Approval: ${currentApproval.charAt(0).toUpperCase() + currentApproval.slice(1)}`;
            approvalSpan.className = `status-indicator approval-${currentApproval}`;
            
            // Update activation indicator
            const activationSpan = statusIndicators.children[1];
            activationSpan.textContent = `Activation: ${currentActivation.charAt(0).toUpperCase() + currentActivation.slice(1)}`;
            activationSpan.className = `status-indicator activation-${currentActivation}`;
            
            // Update description
            let desc = '';
            if (currentApproval === 'all' && currentActivation === 'all') {
                desc = 'Showing all users regardless of approval or activation status';
            } else if (currentApproval === 'all') {
                desc = `Showing all users who are ${currentActivation}`;
            } else if (currentActivation === 'all') {
                desc = `Showing all ${currentApproval} users`;
            } else {
                desc = `Showing ${currentApproval} users who are ${currentActivation}`;
            }
            statusDescription.textContent = desc;
        }

        // Initialize
        updateStatusDisplay();
    </script>
</body>
</html>
