<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Level Management</title>
</head>
<body>
    <h1>Level Management API Test</h1>
    
    <div>
        <h2>Get Next Level Number</h2>
        <button onclick="getNextLevel()">Get Next Level</button>
        <div id="nextLevelResult"></div>
    </div>
    
    <div>
        <h2>Add Level</h2>
        <input type="number" id="levelNum" placeholder="Level Number" value="12">
        <select id="gameType">
            <option value="hangman">Hangman</option>
            <option value="matching">Matching</option>
            <option value="millionaire">Millionaire</option>
            <option value="robot-battle">Robot Battle</option>
        </select>
        <button onclick="addLevel()">Add Level</button>
        <div id="addLevelResult"></div>
        <p><small>Note: This will now also create a blank record in the game_content table</small></p>
    </div>
    
    <div>
        <h2>Delete Level</h2>
        <input type="number" id="deleteLevelNum" placeholder="Level Number" value="12">
        <button onclick="deleteLevel()">Delete Level</button>
        <div id="deleteLevelResult"></div>
    </div>

    <div>
        <h2>Check Database Content</h2>
        <input type="number" id="checkLevelNum" placeholder="Level Number" value="12">
        <button onclick="checkLevelContent()">Check Level Content</button>
        <div id="checkLevelResult"></div>
    </div>

    <div>
        <h2>Swap Levels</h2>
        <input type="number" id="swapLevel1" placeholder="Level 1" value="1">
        <input type="number" id="swapLevel2" placeholder="Level 2" value="2">
        <button onclick="swapLevels()">Swap Levels</button>
        <div id="swapLevelsResult"></div>
    </div>

    <div>
        <h2>Rearrange Levels</h2>
        <textarea id="levelMappings" placeholder='[{"old_level": 1, "new_level": 3}, {"old_level": 3, "new_level": 1}]' rows="3" cols="50"></textarea>
        <button onclick="rearrangeLevels()">Rearrange Levels</button>
        <div id="rearrangeLevelsResult"></div>
        <p><small>Enter JSON array of level mappings</small></p>
    </div>

    <script>
        const API_BASE_URL = 'php/admin/';
        
        async function getNextLevel() {
            try {
                const response = await fetch(`${API_BASE_URL}level_management_api.php?action=get_next_level`);
                const result = await response.json();
                document.getElementById('nextLevelResult').innerHTML = JSON.stringify(result, null, 2);
            } catch (error) {
                document.getElementById('nextLevelResult').innerHTML = 'Error: ' + error.message;
            }
        }
        
        async function addLevel() {
            const levelNumber = document.getElementById('levelNum').value;
            const gameCategory = document.getElementById('gameType').value;
            
            try {
                const response = await fetch(`${API_BASE_URL}level_management_api.php?action=add_level`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        level_number: parseInt(levelNumber),
                        game_category: gameCategory
                    })
                });
                
                const result = await response.json();
                document.getElementById('addLevelResult').innerHTML = JSON.stringify(result, null, 2);
            } catch (error) {
                document.getElementById('addLevelResult').innerHTML = 'Error: ' + error.message;
            }
        }
        
        async function deleteLevel() {
            const levelNumber = document.getElementById('deleteLevelNum').value;

            try {
                const response = await fetch(`${API_BASE_URL}level_management_api.php?action=delete_level&level_number=${levelNumber}`, {
                    method: 'DELETE'
                });

                const result = await response.json();
                document.getElementById('deleteLevelResult').innerHTML = JSON.stringify(result, null, 2);
            } catch (error) {
                document.getElementById('deleteLevelResult').innerHTML = 'Error: ' + error.message;
            }
        }

        async function checkLevelContent() {
            const levelNumber = document.getElementById('checkLevelNum').value;

            try {
                const response = await fetch(`${API_BASE_URL}game_content_api.php?action=get_content_by_level&level=${levelNumber}`);
                const result = await response.json();
                document.getElementById('checkLevelResult').innerHTML = JSON.stringify(result, null, 2);
            } catch (error) {
                document.getElementById('checkLevelResult').innerHTML = 'Error: ' + error.message;
            }
        }

        async function swapLevels() {
            const level1 = document.getElementById('swapLevel1').value;
            const level2 = document.getElementById('swapLevel2').value;

            try {
                const response = await fetch(`${API_BASE_URL}level_management_api.php?action=swap_levels`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        level1: parseInt(level1),
                        level2: parseInt(level2)
                    })
                });

                const result = await response.json();
                document.getElementById('swapLevelsResult').innerHTML = JSON.stringify(result, null, 2);
            } catch (error) {
                document.getElementById('swapLevelsResult').innerHTML = 'Error: ' + error.message;
            }
        }

        async function rearrangeLevels() {
            const mappingsText = document.getElementById('levelMappings').value;

            try {
                const levelMappings = JSON.parse(mappingsText);

                const response = await fetch(`${API_BASE_URL}level_management_api.php?action=rearrange_levels`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        level_mappings: levelMappings
                    })
                });

                const result = await response.json();
                document.getElementById('rearrangeLevelsResult').innerHTML = JSON.stringify(result, null, 2);
            } catch (error) {
                document.getElementById('rearrangeLevelsResult').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
