# Level Management Features - User Guide

## Overview
The admin game management system now includes level management features that allow you to add and delete game levels dynamically.

## Features Added

### 1. Add Level Feature
- **Location**: Admin Game Management page
- **Button**: "Add Level" button in the content controls section
- **Functionality**:
  - Automatically suggests the next available level number
  - Allows selection of game category (<PERSON><PERSON>, Matching, Millionaire, Robot Battle)
  - Creates a new gameX.html file based on the selected template
  - **Creates a blank record in the game_content table** with only level_number filled
  - Properly adjusts file paths and level-specific content
  - Uses database transactions for consistency

### 2. Delete Level Feature
- **Location**: Each level card in the admin interface
- **Button**: "Delete Level" button (red trash icon)
- **Functionality**:
  - Shows detailed confirmation modal with impact summary
  - Deletes the level HTML file (gameX.html)
  - Removes all questions associated with the level from the database
  - Removes all user progress for that level
  - Uses database transactions with rollback on failure

### 3. Rearrange Levels Feature
- **Location**: Admin Game Management page
- **Button**: "Rearrange Levels" button in the content controls section
- **Functionality**:
  - **Quick Swap**: Swap positions of any two levels instantly
  - **Drag & Drop**: Reorder multiple levels by dragging them to new positions (no tilting effect)
  - Updates level numbers in database and renames HTML files accordingly
  - Preserves all questions and user progress during rearrangement
  - Uses database transactions for consistency

### 4. View All Questions Feature
- **Location**: Admin Game Management page
- **Button**: "View All Questions" button in the content controls section
- **Functionality**:
  - **Modal Interface**: Shows all questions in a comprehensive modal instead of expanding inline
  - **Statistics Dashboard**: Displays total levels, questions, and question type counts
  - **Advanced Filtering**: Search by question text, filter by level, or filter by question type
  - **Question Cards**: Each question displayed as a card with level badge and type indicator
  - **Responsive Design**: Works on desktop and mobile devices

### 5. Question Limit System
- **Maximum Questions Per Level**: 50 questions per level
- **Visual Indicators**: Question count shows current/maximum (e.g., "25/50 questions")
- **Button States**: "Add Question" button becomes disabled when limit is reached
- **Limit Reached Warning**: Red "Limit reached" badge appears when maximum is hit
- **Prevention**: System prevents adding questions beyond the 50-question limit

## How to Use

### Adding a New Level
1. Go to the Admin Game Management page
2. Click the "Add Level" button in the top controls
3. The modal will show the next available level number
4. Select the game category you want to copy from:
   - **Hangman**: Robot Rescue Quiz game
   - **Matching**: Drag and drop matching game
   - **Millionaire**: Quiz with lifelines
   - **Robot Battle**: Battle-themed quiz
5. Click "Create Level"
6. The system will create the new level file and refresh the display

### Deleting a Level
1. Find the level you want to delete in the admin interface
2. Click the "Delete Level" button (red trash icon) on the level card
3. Review the detailed confirmation modal showing:
   - Level number to be deleted
   - Number of questions that will be removed
   - Warning about permanent data loss
4. Click "Delete Level" to confirm or "Cancel" to abort
5. The system will:
   - Delete the HTML file
   - Remove all questions for that level
   - Remove all user progress for that level

### Rearranging Levels

#### Quick Swap Method
1. Click the "Rearrange Levels" button in the admin interface
2. In the "Quick Swap" section:
   - Select the first level from the dropdown
   - Select the second level from the dropdown
   - Click "Swap" to exchange their positions
3. The system will instantly swap the two levels

#### Drag & Drop Method
1. Click the "Rearrange Levels" button in the admin interface
2. In the "Drag & Drop Reorder" section:
   - Drag any level item to a new position
   - Drop it where you want it to be positioned
   - Repeat for multiple levels as needed
   - Position indicators update automatically
3. Click "Apply Changes" to save the new order
4. The system will:
   - Update level numbers in the database
   - Rename HTML files to match new positions
   - Preserve all questions and user progress

### Viewing All Questions
1. Click the "View All Questions" button in the admin interface
2. The modal will display:
   - **Statistics**: Total levels, questions, and breakdown by question type
   - **Filters**: Search box, level filter dropdown, and question type filter
   - **Question Cards**: All questions displayed as organized cards
3. Use the filters to find specific questions:
   - **Search**: Type keywords to search question text
   - **Level Filter**: Show questions from specific levels only
   - **Type Filter**: Show only Multiple Choice or Matching questions
4. Each question card shows:
   - Question text and all answer options
   - Level badge indicating which level it belongs to
   - Type badge showing question type (Multiple Choice/Matching)
   - Correct answer highlighted with checkmark

## Technical Details

### File Structure
- Template files: `html/game/hangman.html`, `matching.html`, `millionaire.html`, `robot_battle.html`
- Generated files: `html/game/game1.html`, `game2.html`, etc.
- API endpoint: `php/admin/level_management_api.php`

### Database Impact
- **Add Level**: Creates HTML file AND inserts blank record in `game_content` table with:
  - `level_number`: Set to the new level number
  - `question_text`, `option1-4`, `correct_answer`: Empty strings
  - `can_edit`: Set to 1 (editable)
  - `quiz_type`: Set to 'Multiple Choice'
  - `created_at`, `updated_at`: Auto-generated timestamps
- **Delete Level**: Removes ALL data from `game_content` and `user_levels` tables for the specified level
- **Rearrange Levels**: Updates `level_number` field in both `game_content` and `user_levels` tables while preserving all other data

### Path Adjustments
When copying template files, the system automatically:
- Updates CSS links from `../css/` to `../../css/`
- Updates JS links from `../js/` to `../../js/`
- Updates image links from `../images/` to `../../images/`
- Sets the correct `data-level` attribute
- Updates page titles to include level number

## Safety Features
- Confirmation dialog before deletion
- Transaction-based database operations (rollback on error)
- Validation of level numbers and game categories
- Prevention of duplicate level creation

## API Endpoints
- `GET ?action=get_next_level` - Get next available level number
- `POST ?action=add_level` - Create new level
- `DELETE ?action=delete_level&level_number=X` - Delete level
- `GET ?action=get_existing_levels` - List existing level files
- `PUT ?action=swap_levels` - Swap positions of two levels
- `PUT ?action=rearrange_levels` - Rearrange multiple levels

## Error Handling
The system includes comprehensive error handling for:
- File operation failures
- Database connection issues
- Invalid input validation
- Permission problems
- Template file missing errors

## Testing
Use the test file `test_level_management.html` to verify API functionality before using the admin interface.
